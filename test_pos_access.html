<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Table Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 POS Table Access Test</h1>
        <p>Testing access to different POS tables to determine which one the API token can access.</p>

        <div class="test-section">
            <h2>Test 1: Fresh POS Table (tblHyyZHUsTdEb3BL)</h2>
            <button onclick="testFreshPOS()">🧪 Test Fresh POS Access</button>
            <div id="fresh-pos-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>Test 2: Legacy POS Table (tbloHzN9XdQLc8xvS)</h2>
            <button onclick="testLegacyPOS()">🧪 Test Legacy POS Access</button>
            <div id="legacy-pos-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>Test 3: Google Ads Table (tblRBXdh6L6zm9CZn) - Known Working</h2>
            <button onclick="testGoogleAds()">✅ Test Google Ads Access</button>
            <div id="google-ads-result" class="result"></div>
        </div>
    </div>

    <script>
        async function testFreshPOS() {
            const resultDiv = document.getElementById('fresh-pos-result');
            resultDiv.textContent = '🔄 Testing Fresh POS table access...';
            resultDiv.className = 'result info';

            try {
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test Fresh POS table
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL&maxRecords=10&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS! Fresh POS table is accessible.
Records found: ${data.records ? data.records.length : 0}
Total available: ${data.total || 'Unknown'}
Response status: ${response.status}

Sample record fields: ${data.records && data.records[0] ? Object.keys(data.records[0].fields).join(', ') : 'No records'}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ FAILED: ${error.message}

This means the API token does not have access to the Fresh POS table.`;
            }
        }

        async function testLegacyPOS() {
            const resultDiv = document.getElementById('legacy-pos-result');
            resultDiv.textContent = '🔄 Testing Legacy POS table access...';
            resultDiv.className = 'result info';

            try {
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test Legacy POS table
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tbloHzN9XdQLc8xvS&maxRecords=10&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS! Legacy POS table is accessible.
Records found: ${data.records ? data.records.length : 0}
Total available: ${data.total || 'Unknown'}
Response status: ${response.status}

Sample record fields: ${data.records && data.records[0] ? Object.keys(data.records[0].fields).join(', ') : 'No records'}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ FAILED: ${error.message}

This confirms the API token does not have access to the Legacy POS table.`;
            }
        }

        async function testGoogleAds() {
            const resultDiv = document.getElementById('google-ads-result');
            resultDiv.textContent = '🔄 Testing Google Ads table access...';
            resultDiv.className = 'result info';

            try {
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test Google Ads table
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn&maxRecords=10&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ SUCCESS! Google Ads table is accessible.
Records found: ${data.records ? data.records.length : 0}
Total available: ${data.total || 'Unknown'}
Response status: ${response.status}

Sample record fields: ${data.records && data.records[0] ? Object.keys(data.records[0].fields).join(', ') : 'No records'}`;

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ FAILED: ${error.message}

This would be unexpected since Google Ads was working in the server logs.`;
            }
        }
    </script>
</body>
</html>
