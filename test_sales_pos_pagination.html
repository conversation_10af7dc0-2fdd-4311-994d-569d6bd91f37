<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Report POS Data Pagination Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        .info { border-left: 4px solid #17a2b8; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Sales Report POS Data Pagination Test</h1>
        <p>This test verifies that the Sales Report is fetching all 1,514 POS records correctly.</p>
        
        <div class="test-section">
            <h3>Test 1: Direct API Call (Raw Server Response)</h3>
            <button onclick="testDirectAPI()">Test Direct API Call</button>
            <div id="direct-api-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: AirtableService.getPOSData() Method</h3>
            <button onclick="testAirtableService()">Test AirtableService Method</button>
            <div id="airtable-service-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Sales Report loadAllSalesData() Function</h3>
            <button onclick="testSalesReportLoad()">Test Sales Report Load</button>
            <div id="sales-report-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Compare with Other Reports</h3>
            <button onclick="testOtherReports()">Test Other Reports</button>
            <div id="other-reports-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Test 5: Check Current Sales Report Data</h3>
            <button onclick="checkCurrentSalesData()">Check Current Sales Data</button>
            <div id="current-sales-result" class="result"></div>
        </div>
    </div>

    <script>
        // Include the AirtableDataService from the main script
        const AIRTABLE_CONFIG = {
            baseId: 'app7ffftdM6e3yekG',
            tables: {
                ghl: 'tblJGJmKqaOaQdJpL',
                pos: 'tbloHzN9XdQLc8xvS',
                googleAds: 'tblJGJmKqaOaQdJpL',
                metaAdsFull: 'tblJGJmKqaOaQdJpL'
            }
        };

        class AirtableDataService {
            constructor() {
                this.baseUrl = '/api/airtable';
                this.cache = new Map();
                this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
                this.localStoragePrefix = 'airtable_cache_';
                this.defaultDateRange = 30;
                this.enableDateFiltering = true;
            }

            async fetchTableData(tableName, options = {}) {
                const { forceRefresh = false, dateFilter = true, disableCache = false, ...apiOptions } = options;

                try {
                    console.log(`🔄 Fetching ALL ${tableName} data from Airtable (server handles pagination)...`);

                    const defaultOptions = {
                        ...apiOptions
                    };

                    const queryParams = new URLSearchParams({
                        baseId: AIRTABLE_CONFIG.baseId,
                        tableId: AIRTABLE_CONFIG.tables[tableName],
                        ...defaultOptions
                    });

                    console.log('🔧 DEBUGGING: Query parameters for', tableName, ':', Object.fromEntries(queryParams));

                    const response = await fetch(`${this.baseUrl}/records?${queryParams}`);

                    if (!response.ok) {
                        throw new Error(`Failed to fetch ${tableName}: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();
                    const allRecords = Array.isArray(data) ? data : (data.records || []);

                    if (data.pagination_info) {
                        console.log(`📊 Server pagination: ${data.pagination_info.total_records} records from ${data.pagination_info.pages_fetched} pages`);
                    }

                    console.log(`✅ Successfully loaded ${allRecords.length} records from ${tableName}`);
                    return allRecords;

                } catch (error) {
                    console.error(`Error fetching ${tableName} data:`, error);
                    throw error;
                }
            }

            async getPOSData(filters = {}) {
                return await this.fetchTableData('pos', filters);
            }
        }

        const airtableService = new AirtableDataService();

        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-api-result');
            resultDiv.innerHTML = '<div class="loading">Testing direct API call...</div>';
            
            try {
                console.log('🧪 Testing direct API call to POS data...');
                
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tbloHzN9XdQLc8xvS');
                const data = await response.json();
                
                const recordCount = data.records ? data.records.length : (Array.isArray(data) ? data.length : 0);
                
                let result = `✅ Direct API Test Results:\n`;
                result += `📊 Total records: ${recordCount}\n`;
                result += `🔧 Response type: ${Array.isArray(data) ? 'Array' : 'Object'}\n`;
                
                if (data.pagination_info) {
                    result += `📄 Pages fetched: ${data.pagination_info.pages_fetched}\n`;
                    result += `🔄 Server-side pagination: ${data.pagination_info.server_side_pagination}\n`;
                    result += `🗑️ Duplicates removed: ${data.pagination_info.duplicates_removed}\n`;
                }
                
                if (recordCount === 1514) {
                    result += `\n🎯 SUCCESS: Got all 1,514 records!`;
                    resultDiv.className = 'result success';
                } else if (recordCount === 1000) {
                    result += `\n⚠️ WARNING: Only got 1,000 records - pagination issue!`;
                    resultDiv.className = 'result warning';
                } else {
                    result += `\n❌ UNEXPECTED: Got ${recordCount} records (expected 1,514)`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Direct API test error:', error);
            }
        }

        async function testAirtableService() {
            const resultDiv = document.getElementById('airtable-service-result');
            resultDiv.innerHTML = '<div class="loading">Testing AirtableService.getPOSData()...</div>';
            
            try {
                console.log('🧪 Testing AirtableService.getPOSData()...');
                
                const data = await airtableService.getPOSData({ forceRefresh: true, dateFilter: false });
                
                let result = `✅ AirtableService Test Results:\n`;
                result += `📊 Total records: ${data.length}\n`;
                result += `🔧 Data type: ${Array.isArray(data) ? 'Array' : typeof data}\n`;
                
                if (data.length === 1514) {
                    result += `\n🎯 SUCCESS: AirtableService got all 1,514 records!`;
                    resultDiv.className = 'result success';
                } else if (data.length === 1000) {
                    result += `\n⚠️ WARNING: AirtableService only got 1,000 records - pagination issue!`;
                    resultDiv.className = 'result warning';
                } else {
                    result += `\n❌ UNEXPECTED: AirtableService got ${data.length} records (expected 1,514)`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('AirtableService test error:', error);
            }
        }

        async function testSalesReportLoad() {
            const resultDiv = document.getElementById('sales-report-result');
            resultDiv.innerHTML = '<div class="loading">Testing Sales Report loadAllSalesData()...</div>';
            
            try {
                console.log('🧪 Testing Sales Report loadAllSalesData()...');
                
                // Simulate the exact same call as Sales Report
                const posDataAll = await airtableService.getPOSData({ forceRefresh: true, dateFilter: false });
                
                let result = `✅ Sales Report Load Test Results:\n`;
                result += `📊 Total records: ${posDataAll.length}\n`;
                result += `🔧 Data type: ${Array.isArray(posDataAll) ? 'Array' : typeof posDataAll}\n`;
                
                if (posDataAll.length === 1514) {
                    result += `\n🎯 SUCCESS: Sales Report load got all 1,514 records!`;
                    resultDiv.className = 'result success';
                } else if (posDataAll.length === 1000) {
                    result += `\n⚠️ WARNING: Sales Report load only got 1,000 records - pagination issue!`;
                    resultDiv.className = 'result warning';
                } else {
                    result += `\n❌ UNEXPECTED: Sales Report load got ${posDataAll.length} records (expected 1,514)`;
                    resultDiv.className = 'result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Sales Report load test error:', error);
            }
        }

        async function testOtherReports() {
            const resultDiv = document.getElementById('other-reports-result');
            resultDiv.innerHTML = '<div class="loading">Testing other reports for comparison...</div>';
            
            try {
                console.log('🧪 Testing other reports for comparison...');
                
                // Test GHL data (should be much more than 1000)
                const ghlData = await airtableService.fetchTableData('ghl', { forceRefresh: true, dateFilter: false });
                
                let result = `✅ Other Reports Comparison:\n`;
                result += `📊 GHL records: ${ghlData.length}\n`;
                
                if (ghlData.length > 1000) {
                    result += `🎯 GHL pagination working correctly (> 1000 records)\n`;
                } else {
                    result += `⚠️ GHL might have pagination issues (≤ 1000 records)\n`;
                }
                
                resultDiv.className = 'result info';
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Other reports test error:', error);
            }
        }

        async function checkCurrentSalesData() {
            const resultDiv = document.getElementById('current-sales-result');
            resultDiv.innerHTML = '<div class="loading">Checking current Sales Report data...</div>';
            
            try {
                console.log('🧪 Checking current Sales Report data...');
                
                let result = `✅ Current Sales Report Data Status:\n`;
                
                // Check if salesReportData exists
                if (typeof window.salesReportData !== 'undefined' && window.salesReportData) {
                    result += `📊 salesReportData: ${window.salesReportData.length} records\n`;
                } else {
                    result += `❌ salesReportData: Not loaded or undefined\n`;
                }
                
                // Check if salesFilteredData exists
                if (typeof window.salesFilteredData !== 'undefined' && window.salesFilteredData) {
                    result += `📊 salesFilteredData: ${window.salesFilteredData.length} records\n`;
                } else {
                    result += `❌ salesFilteredData: Not loaded or undefined\n`;
                }
                
                // Check if posData exists (legacy)
                if (typeof window.posData !== 'undefined' && window.posData) {
                    result += `📊 posData (legacy): ${window.posData.length} records\n`;
                } else {
                    result += `❌ posData (legacy): Not loaded or undefined\n`;
                }
                
                resultDiv.className = 'result info';
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Current sales data check error:', error);
            }
        }
    </script>
</body>
</html>
