<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Report Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .loading { color: #666; font-style: italic; }
        h1 { color: #333; }
        h2 { color: #555; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Sales Report Fix Verification</h1>
        <p>This test verifies that the Sales Report pagination fix is working and we're getting all 1,514 POS records.</p>
        
        <h2>Quick Test</h2>
        <button onclick="runQuickTest()">🚀 Run Quick Verification</button>
        <div id="quick-test-result"></div>

        <h2>Detailed Analysis</h2>
        <button onclick="runDetailedTest()">🔍 Run Detailed Analysis</button>
        <div id="detailed-test-result"></div>

        <h2>Compare Before/After</h2>
        <button onclick="compareResults()">📊 Compare Data Sources</button>
        <div id="compare-result"></div>
    </div>

    <script>
        // Include necessary configuration
        const AIRTABLE_CONFIG = {
            baseId: 'app7ffftdM6e3yekG',
            tables: {
                pos: 'tbloHzN9XdQLc8xvS'
            }
        };

        async function runQuickTest() {
            const resultDiv = document.getElementById('quick-test-result');
            resultDiv.innerHTML = '<div class="loading">🔄 Running quick verification test...</div>';
            
            try {
                console.log('🚀 Quick test: Checking Sales Report data...');
                
                // Test direct API call
                const response = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tbloHzN9XdQLc8xvS');
                const data = await response.json();
                const recordCount = data.records ? data.records.length : 0;
                
                let result = `✅ Quick Test Results:\n`;
                result += `📊 Direct API call: ${recordCount} records\n`;
                
                if (recordCount === 1514) {
                    result += `🎯 SUCCESS: API returns all 1,514 records!\n`;
                    result += `✅ The pagination fix is working correctly.`;
                    resultDiv.className = 'test-result success';
                } else if (recordCount === 1000) {
                    result += `⚠️ WARNING: API still returns only 1,000 records.\n`;
                    result += `❌ The pagination issue persists.`;
                    resultDiv.className = 'test-result warning';
                } else {
                    result += `❓ UNEXPECTED: API returns ${recordCount} records.\n`;
                    result += `🔍 This needs investigation.`;
                    resultDiv.className = 'test-result info';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Quick test error:', error);
            }
        }

        async function runDetailedTest() {
            const resultDiv = document.getElementById('detailed-test-result');
            resultDiv.innerHTML = '<div class="loading">🔄 Running detailed analysis...</div>';
            
            try {
                console.log('🔍 Detailed test: Analyzing Sales Report initialization...');
                
                let result = `✅ Detailed Analysis Results:\n\n`;
                
                // Test 1: Direct API
                const apiResponse = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tbloHzN9XdQLc8xvS');
                const apiData = await apiResponse.json();
                const apiCount = apiData.records ? apiData.records.length : 0;
                
                result += `1. Direct API Test:\n`;
                result += `   📊 Records: ${apiCount}\n`;
                result += `   🔧 Pagination info: ${apiData.pagination_info ? 'Available' : 'Not available'}\n`;
                if (apiData.pagination_info) {
                    result += `   📄 Pages fetched: ${apiData.pagination_info.pages_fetched}\n`;
                    result += `   🗑️ Duplicates removed: ${apiData.pagination_info.duplicates_removed}\n`;
                }
                result += `\n`;
                
                // Test 2: Check if Sales Report variables exist
                result += `2. Sales Report Variables:\n`;
                if (typeof window.salesReportData !== 'undefined' && window.salesReportData) {
                    result += `   📊 salesReportData: ${window.salesReportData.length} records\n`;
                } else {
                    result += `   ❌ salesReportData: Not loaded\n`;
                }
                
                if (typeof window.salesFilteredData !== 'undefined' && window.salesFilteredData) {
                    result += `   📊 salesFilteredData: ${window.salesFilteredData.length} records\n`;
                } else {
                    result += `   ❌ salesFilteredData: Not loaded\n`;
                }
                
                if (typeof window.posData !== 'undefined' && window.posData) {
                    result += `   📊 posData (legacy): ${window.posData.length} records\n`;
                } else {
                    result += `   ❌ posData (legacy): Not loaded\n`;
                }
                result += `\n`;
                
                // Test 3: Summary
                result += `3. Summary:\n`;
                if (apiCount === 1514) {
                    result += `   🎯 API pagination: WORKING ✅\n`;
                } else {
                    result += `   ❌ API pagination: BROKEN (${apiCount}/1514)\n`;
                }
                
                if (window.salesReportData && window.salesReportData.length === 1514) {
                    result += `   🎯 Sales Report data: WORKING ✅\n`;
                } else if (window.salesReportData) {
                    result += `   ⚠️ Sales Report data: PARTIAL (${window.salesReportData.length}/1514)\n`;
                } else {
                    result += `   ❌ Sales Report data: NOT LOADED\n`;
                }
                
                // Determine overall status
                if (apiCount === 1514 && window.salesReportData && window.salesReportData.length === 1514) {
                    result += `\n🎉 OVERALL STATUS: FIXED ✅`;
                    resultDiv.className = 'test-result success';
                } else if (apiCount === 1514) {
                    result += `\n⚠️ OVERALL STATUS: API FIXED, SALES REPORT NEEDS REFRESH`;
                    resultDiv.className = 'test-result warning';
                } else {
                    result += `\n❌ OVERALL STATUS: STILL BROKEN`;
                    resultDiv.className = 'test-result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Detailed test error:', error);
            }
        }

        async function compareResults() {
            const resultDiv = document.getElementById('compare-result');
            resultDiv.innerHTML = '<div class="loading">🔄 Comparing data sources...</div>';
            
            try {
                console.log('📊 Comparing different data sources...');
                
                let result = `📊 Data Source Comparison:\n\n`;
                
                // API call
                const apiResponse = await fetch('/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tbloHzN9XdQLc8xvS');
                const apiData = await apiResponse.json();
                const apiCount = apiData.records ? apiData.records.length : 0;
                
                result += `🌐 Direct API Call: ${apiCount} records\n`;
                
                // Check various global variables
                const dataSources = [
                    { name: 'salesReportData', data: window.salesReportData },
                    { name: 'salesFilteredData', data: window.salesFilteredData },
                    { name: 'posData (legacy)', data: window.posData },
                    { name: 'allCsvData', data: window.allCsvData }
                ];
                
                dataSources.forEach(source => {
                    if (source.data && Array.isArray(source.data)) {
                        result += `📊 ${source.name}: ${source.data.length} records\n`;
                    } else {
                        result += `❌ ${source.name}: Not available\n`;
                    }
                });
                
                result += `\n📈 Expected: 1,514 POS records\n`;
                result += `🎯 Target: All sources should show 1,514 records\n\n`;
                
                // Analysis
                const workingSources = dataSources.filter(s => s.data && s.data.length === 1514).length;
                const totalSources = dataSources.filter(s => s.data).length;
                
                if (apiCount === 1514 && workingSources === totalSources && totalSources > 0) {
                    result += `🎉 EXCELLENT: All data sources are working correctly!`;
                    resultDiv.className = 'test-result success';
                } else if (apiCount === 1514) {
                    result += `⚠️ PARTIAL: API is fixed, but some app variables need refresh.`;
                    resultDiv.className = 'test-result warning';
                } else {
                    result += `❌ ISSUE: API pagination is still broken.`;
                    resultDiv.className = 'test-result error';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Compare test error:', error);
            }
        }
    </script>
</body>
</html>
