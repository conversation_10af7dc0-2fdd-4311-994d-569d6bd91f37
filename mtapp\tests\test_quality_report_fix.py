#!/usr/bin/env python3
"""
Test script to verify the Google Ads quality report generation fix
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

def test_quality_report_generation():
    """Test the Google Ads quality report generation"""
    print("Testing Google Ads Quality Report Generation...")
    print("=" * 50)
    
    try:
        # Load Google Ads data
        df = pd.read_csv('gads.csv')
        
        # Clean column names
        df.columns = [c.strip() for c in df.columns]
        
        # Convert and validate data types
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        
        # Convert numeric columns
        numeric_columns = ['Cost', 'Impressions', 'Clicks', 'Conversions', 'CTR', 'CPC', 'Conv. Rate', 'Cost per Conv.']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove rows with invalid dates
        initial_count = len(df)
        df = df[df['Date'].notnull()].copy()
        
        # Create quality report data (simulating what the app does)
        quality_report = {
            'initial_rows': initial_count,
            'valid_rows': len(df),
            'date_range': (df['Date'].min(), df['Date'].max()),
            'total_cost': df['Cost'].sum(),
            'total_clicks': df['Clicks'].sum(),
            'total_impressions': df['Impressions'].sum(),
            'total_conversions': df['Conversions'].sum(),
            'unique_campaigns': df['Campaign ID'].nunique()
        }
        
        # Calculate additional metrics (same as in the app)
        avg_ctr = (df['Clicks'].sum() / df['Impressions'].sum() * 100) if df['Impressions'].sum() > 0 else 0
        avg_cpc = (df['Cost'].sum() / df['Clicks'].sum()) if df['Clicks'].sum() > 0 else 0
        conv_rate = (df['Conversions'].sum() / df['Clicks'].sum() * 100) if df['Clicks'].sum() > 0 else 0
        cost_per_conv = (quality_report['total_cost'] / quality_report['total_conversions']) if quality_report['total_conversions'] > 0 else 0
        
        # Generate the quality report text (same format as in the app)
        quality_text = f"""Google Ads Data Quality Report
{'='*50}

Data Validation:
• Initial rows loaded: {quality_report['initial_rows']:,}
• Valid rows after cleaning: {quality_report['valid_rows']:,}
• Data quality: {(quality_report['valid_rows']/quality_report['initial_rows']*100):.1f}%

Date Coverage:
• Start date: {quality_report['date_range'][0].strftime('%Y-%m-%d')}
• End date: {quality_report['date_range'][1].strftime('%Y-%m-%d')}
• Total days: {(quality_report['date_range'][1] - quality_report['date_range'][0]).days + 1}

Campaign Summary:
• Unique campaigns: {quality_report['unique_campaigns']:,}
• Total cost: ${quality_report['total_cost']:,.2f}
• Total clicks: {quality_report['total_clicks']:,.0f}
• Total impressions: {quality_report['total_impressions']:,.0f}
• Total conversions: {quality_report['total_conversions']:,.0f}

Performance Metrics:
• Average CTR: {avg_ctr:.2f}%
• Average CPC: ${avg_cpc:.2f}
• Conversion Rate: {conv_rate:.2f}%
• Cost per Conversion: ${cost_per_conv:.2f}

Top Campaigns by Conversions:
"""
        
        # Add top campaigns
        top_campaigns = df.groupby('Campaign Name').agg({
            'Conversions': 'sum',
            'Cost': 'sum',
            'Clicks': 'sum'
        }).sort_values('Conversions', ascending=False).head(5)
        
        for campaign, data in top_campaigns.iterrows():
            quality_text += f"• {campaign[:30]}: {data['Conversions']:.0f} conversions, ${data['Cost']:.2f} cost\n"
        
        print("✓ Quality report generated successfully!")
        print("\nSample of the report:")
        print("-" * 30)
        print(quality_text[:500] + "...")
        
        return True
        
    except Exception as e:
        print(f"✗ Quality report generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the quality report test"""
    print("Google Ads Quality Report Fix Verification")
    print("=" * 60)
    
    success = test_quality_report_generation()
    
    if success:
        print("\n🎉 Quality report generation is working correctly!")
        print("The f-string formatting error has been fixed.")
    else:
        print("\n❌ Quality report generation still has issues.")

if __name__ == "__main__":
    main()
