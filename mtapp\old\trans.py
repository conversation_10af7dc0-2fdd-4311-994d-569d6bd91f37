# Create the corrected application file
app_code = '''
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime
import os
import traceback

class MetaAdsTransformer:
    def __init__(self, root):
        self.root = root
        self.root.title("Meta Ads Data Transformer - Accuracy First")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # Data storage
        self.raw_data = None
        self.monthly_summary = None
        self.daily_aggregated = None
        self.data_quality_report = {}
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main container
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Meta Ads Data Transformer", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control Panel
        self.setup_control_panel(main_frame)
        
        # Data Quality Panel
        self.setup_quality_panel(main_frame)
        
        # Data Preview Panel
        self.setup_preview_panel(main_frame)
        
        # Status Bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Load a CSV file to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def setup_control_panel(self, parent):
        # Control Panel Frame
        control_frame = ttk.LabelFrame(parent, text="Controls", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        
        # Load CSV Button
        load_btn = ttk.Button(control_frame, text="📁 Load CSV", 
                             command=self.load_csv, width=15)
        load_btn.grid(row=0, column=0, pady=5, sticky=tk.W)
        
        # Transform Options
        ttk.Label(control_frame, text="Transform Options:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        
        self.daily_var = tk.BooleanVar(value=True)
        self.monthly_var = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(control_frame, text="Daily Aggregation", 
                       variable=self.daily_var).grid(row=2, column=0, sticky=tk.W)
        ttk.Checkbutton(control_frame, text="Monthly Summary", 
                       variable=self.monthly_var).grid(row=3, column=0, sticky=tk.W)
        
        # Transform Button
        transform_btn = ttk.Button(control_frame, text="🔄 Transform Data", 
                                  command=self.transform_data, width=15)
        transform_btn.grid(row=4, column=0, pady=(10, 5), sticky=tk.W)
        
        # Export Buttons
        ttk.Label(control_frame, text="Export Options:").grid(row=5, column=0, sticky=tk.W, pady=(10, 5))
        
        export_daily_btn = ttk.Button(control_frame, text="💾 Export Daily", 
                                     command=self.export_daily, width=15)
        export_daily_btn.grid(row=6, column=0, pady=2, sticky=tk.W)
        
        export_monthly_btn = ttk.Button(control_frame, text="💾 Export Monthly", 
                                       command=self.export_monthly, width=15)
        export_monthly_btn.grid(row=7, column=0, pady=2, sticky=tk.W)
        
    def setup_quality_panel(self, parent):
        # Data Quality Panel
        quality_frame = ttk.LabelFrame(parent, text="Data Quality Report", padding="10")
        quality_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N), padx=(0, 10))
        quality_frame.columnconfigure(0, weight=1)
        
        # Quality metrics display
        self.quality_text = tk.Text(quality_frame, height=12, width=40, 
                                   font=('Courier', 9), state=tk.DISABLED)
        quality_scroll = ttk.Scrollbar(quality_frame, orient=tk.VERTICAL, 
                                      command=self.quality_text.yview)
        self.quality_text.configure(yscrollcommand=quality_scroll.set)
        
        self.quality_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        quality_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
    def setup_preview_panel(self, parent):
        # Data Preview Panel
        preview_frame = ttk.LabelFrame(parent, text="Data Preview", padding="10")
        preview_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        
        # Notebook for different views
        self.notebook = ttk.Notebook(preview_frame)
        self.notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        # Monthly Summary Tab
        self.setup_monthly_tab()
        
        # Daily Data Tab
        self.setup_daily_tab()
        
        # Raw Data Tab
        self.setup_raw_tab()
        
    def setup_monthly_tab(self):
        monthly_frame = ttk.Frame(self.notebook)
        self.notebook.add(monthly_frame, text="Monthly Summary")
        
        # Monthly summary table
        columns = ('Month', 'Amount Spent (USD)', 'Results', 'Entry Count', 'Avg Frequency')
        self.monthly_tree = ttk.Treeview(monthly_frame, columns=columns, show='headings', height=10)
        
        # Configure columns
        for col in columns:
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=120, anchor=tk.CENTER)
        
        # Scrollbars
        monthly_scroll_y = ttk.Scrollbar(monthly_frame, orient=tk.VERTICAL, 
                                        command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_frame, orient=tk.HORIZONTAL, 
                                        command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set, 
                                   xscrollcommand=monthly_scroll_x.set)
        
        self.monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monthly_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        monthly_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        monthly_frame.columnconfigure(0, weight=1)
        monthly_frame.rowconfigure(0, weight=1)
        
    def setup_daily_tab(self):
        daily_frame = ttk.Frame(self.notebook)
        self.notebook.add(daily_frame, text="Daily Aggregated")
        
        # Daily data table
        columns = ('Date', 'Total Spend', 'Total Results', 'Total Reach', 'Total Impressions', 'Frequency')
        self.daily_tree = ttk.Treeview(daily_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=100, anchor=tk.CENTER)
        
        daily_scroll_y = ttk.Scrollbar(daily_frame, orient=tk.VERTICAL, 
                                      command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_frame, orient=tk.HORIZONTAL, 
                                      command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set, 
                                 xscrollcommand=daily_scroll_x.set)
        
        self.daily_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        daily_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        daily_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        daily_frame.columnconfigure(0, weight=1)
        daily_frame.rowconfigure(0, weight=1)
        
    def setup_raw_tab(self):
        raw_frame = ttk.Frame(self.notebook)
        self.notebook.add(raw_frame, text="Raw Data Sample")
        
        # Raw data table (first 100 rows)
        columns = ('Date', 'Ad Name', 'Amount Spent', 'Results', 'Reach', 'Impressions')
        self.raw_tree = ttk.Treeview(raw_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.raw_tree.heading(col, text=col)
            self.raw_tree.column(col, width=120, anchor=tk.CENTER)
        
        raw_scroll_y = ttk.Scrollbar(raw_frame, orient=tk.VERTICAL, 
                                    command=self.raw_tree.yview)
        raw_scroll_x = ttk.Scrollbar(raw_frame, orient=tk.HORIZONTAL, 
                                    command=self.raw_tree.xview)
        self.raw_tree.configure(yscrollcommand=raw_scroll_y.set, 
                               xscrollcommand=raw_scroll_x.set)
        
        self.raw_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        raw_scroll_y.grid(row=0, column=1, sticky=(tk.N, tk.S))
        raw_scroll_x.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        raw_frame.columnconfigure(0, weight=1)
        raw_frame.rowconfigure(0, weight=1)
        
    def load_csv(self):
        try:
            file_path = filedialog.askopenfilename(
                title="Select Meta Ads CSV File",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )
            
            if not file_path:
                return
                
            self.status_var.set("Loading CSV file...")
            self.root.update()
            
            # Load and validate data
            self.raw_data = self.load_and_validate_csv(file_path)
            
            if self.raw_data is not None:
                self.update_raw_data_display()
                self.generate_quality_report()
                self.status_var.set(f"Loaded {len(self.raw_data)} records successfully")
            else:
                self.status_var.set("Failed to load CSV file")
                
        except Exception as e:
            self.handle_error("Error loading CSV", e)
            
    def load_and_validate_csv(self, file_path):
        try:
            # Load CSV
            df = pd.read_csv(file_path)
            
            # Skip summary row (first row)
            if len(df) > 1:
                df = df.iloc[1:].copy()
            
            # Clean column names
            df.columns = [c.strip() for c in df.columns]
            
            # Validate required columns
            required_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                messagebox.showerror("Missing Columns", 
                                   f"Required columns missing: {', '.join(missing_columns)}")
                return None
            
            # Convert and validate data types
            df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
            
            for col in ['Amount spent (USD)', 'Results', 'Reach', 'Impressions']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Remove rows with invalid dates
            initial_count = len(df)
            df = df[df['Reporting ends'].notnull()].copy()
            
            if len(df) == 0:
                messagebox.showerror("Data Error", "No valid data found after cleaning")
                return None
            
            # Store validation info
            self.data_quality_report = {
                'initial_rows': initial_count,
                'valid_rows': len(df),
                'date_range': (df['Reporting ends'].min(), df['Reporting ends'].max()),
                'total_spend': df['Amount spent (USD)'].sum(),
                'total_results': df['Results'].sum()
            }
            
            return df
            
        except Exception as e:
            messagebox.showerror("File Error", f"Error reading CSV file: {str(e)}")
            return None
            
    def transform_data(self):
        if self.raw_data is None:
            messagebox.showwarning("No Data", "Please load a CSV file first")
            return
            
        try:
            self.status_var.set("Transforming data...")
            self.root.update()
            
            if self.monthly_var.get():
                self.monthly_summary = self.aggregate_by_period(self.raw_data, 'M')
                self.update_monthly_display()
                
            if self.daily_var.get():
                self.daily_aggregated = self.aggregate_by_period(self.raw_data, 'D')
                self.update_daily_display()
                
            self.status_var.set("Data transformation completed successfully")
            
        except Exception as e:
            self.handle_error("Error transforming data", e)
            
    def aggregate_by_period(self, df, period):
        try:
            # Group by period
            grouped = df.groupby(df['Reporting ends'].dt.to_period(period)).agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Ad name': 'count'  # Count entries
            }).reset_index()
            
            # Rename columns
            grouped = grouped.rename(columns={
                'Reporting ends': 'Period',
                'Amount spent (USD)': 'Total_Spend',
                'Results': 'Total_Results',
                'Reach': 'Total_Reach',
                'Impressions': 'Total_Impressions',
                'Ad name': 'Entry_Count'
            })
            
            # Calculate frequency (handle division by zero)
            grouped['Frequency'] = np.where(
                grouped['Total_Reach'] > 0,
                grouped['Total_Impressions'] / grouped['Total_Reach'],
                0
            )
            
            # Format period
            grouped['Period'] = grouped['Period'].astype(str)
            
            return grouped
            
        except Exception as e:
            raise Exception(f"Error in aggregation: {str(e)}")
            
    def update_monthly_display(self):
        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)
            
        if self.monthly_summary is not None:
            for _, row in self.monthly_summary.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Spend']:,.2f}",
                    f"{row['Total_Results']:,.0f}",
                    f"{row['Entry_Count']:,}",
                    f"{row['Frequency']:.2f}"
                )
                self.monthly_tree.insert('', 'end', values=values)
                
    def update_daily_display(self):
        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)
            
        if self.daily_aggregated is not None:
            # Show first 50 rows to avoid overwhelming the display
            display_data = self.daily_aggregated.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Spend']:,.2f}",
                    f"{row['Total_Results']:,.0f}",
                    f"{row['Total_Reach']:,.0f}",
                    f"{row['Total_Impressions']:,.0f}",
                    f"{row['Frequency']:.2f}"
                )
                self.daily_tree.insert('', 'end', values=values)
                
    def update_raw_data_display(self):
        # Clear existing data
        for item in self.raw_tree.get_children():
            self.raw_tree.delete(item)
            
        if self.raw_data is not None:
            # Show first 50 rows
            display_data = self.raw_data.head(50)
            for _, row in display_data.iterrows():
                values = (
                    row['Reporting ends'].strftime('%Y-%m-%d') if pd.notnull(row['Reporting ends']) else 'Invalid',
                    str(row.get('Ad name', 'N/A'))[:20],  # Truncate long names
                    f"${row['Amount spent (USD)']:,.2f}" if pd.notnull(row['Amount spent (USD)']) else '$0.00',
                    f"{row['Results']:,.0f}" if pd.notnull(row['Results']) else '0',
                    f"{row['Reach']:,.0f}" if pd.notnull(row['Reach']) else '0',
                    f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else '0'
                )
                self.raw_tree.insert('', 'end', values=values)
                
    def generate_quality_report(self):
        if self.raw_data is None:
            return
            
        try:
            report = []
            report.append("=== DATA QUALITY REPORT ===\\n")
            
            # Basic stats
            report.append(f"Total Records: {len(self.raw_data):,}")
            report.append(f"Date Range: {self.data_quality_report['date_range'][0].strftime('%Y-%m-%d')} to {self.data_quality_report['date_range'][1].strftime('%Y-%m-%d')}")
            report.append(f"Total Spend: ${self.data_quality_report['total_spend']:,.2f}")
            report.append(f"Total Results: {self.data_quality_report['total_results']:,.0f}")
            report.append("")
            
            # Data completeness
            report.append("=== DATA COMPLETENESS ===")
            for col in ['Amount spent (USD)', 'Results', 'Reach', 'Impressions']:
                null_count = self.raw_data[col].isnull().sum()
                null_pct = (null_count / len(self.raw_data)) * 100
                report.append(f"{col}: {null_pct:.1f}% missing ({null_count:,} records)")
            report.append("")
            
            # Monthly breakdown
            report.append("=== MONTHLY BREAKDOWN ===")
            monthly_stats = self.raw_data.groupby(self.raw_data['Reporting ends'].dt.to_period('M')).agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Ad name': 'count'
            })
            
            for period, row in monthly_stats.iterrows():
                report.append(f"{period}: ${row['Amount spent (USD)']:,.2f}, {row['Results']:,.0f} results, {row['Ad name']:,} entries")
            
            # Display report
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, "\\n".join(report))
            self.quality_text.config(state=tk.DISABLED)
            
        except Exception as e:
            self.handle_error("Error generating quality report", e)
            
    def export_daily(self):
        if self.daily_aggregated is None:
            messagebox.showwarning("No Data", "Please transform data first")
            return
            
        self.export_data(self.daily_aggregated, "daily_aggregated")
        
    def export_monthly(self):
        if self.monthly_summary is None:
            messagebox.showwarning("No Data", "Please transform data first")
            return
            
        self.export_data(self.monthly_summary, "monthly_summary")
        
    def export_data(self, data, default_name):
        try:
            file_path = filedialog.asksaveasfilename(
                title=f"Save {default_name.replace('_', ' ').title()}",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialfile=f"meta_ads_{default_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )
            
            if file_path:
                data.to_csv(file_path, index=False)
                messagebox.showinfo("Export Successful", f"Data exported to {file_path}")
                self.status_var.set(f"Exported {len(data)} records to {os.path.basename(file_path)}")
                
        except Exception as e:
            self.handle_error("Error exporting data", e)
            
    def handle_error(self, title, error):
        error_msg = f"{title}: {str(error)}"
        print(f"ERROR: {error_msg}")
        print(traceback.format_exc())
        messagebox.showerror(title, error_msg)
        self.status_var.set(f"Error: {str(error)}")

def main():
    root = tk.Tk()
    app = MetaAdsTransformer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
'''

# Save the corrected application to a file
with open('meta_ads_transformer_fixed.py', 'w', encoding='utf-8') as f:
    f.write(app_code)

print("Fixed Meta Ads Transformer application created successfully!")
print("File saved as: meta_ads_transformer_fixed.py")
print("\\nChanges made:")
print("- Fixed export dialog error: changed 'initialvalue' to 'initialfile'")
print("- All other functionality remains the same")
print("\\nTo run the corrected application:")
print("1. Make sure you have pandas and numpy installed: pip install pandas numpy")
print("2. Run: python meta_ads_transformer_fixed.py")