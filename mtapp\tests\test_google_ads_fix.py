#!/usr/bin/env python3
"""
Test Google Ads CSV loading fix
"""

import sys
import os
sys.path.append('..')

def test_google_ads_column_mapping():
    """Test that Google Ads CSV columns are properly mapped"""
    print("🔍 TESTING GOOGLE ADS COLUMN MAPPING")
    print("=" * 60)
    
    try:
        import pandas as pd
        
        # Test the column mapping logic
        print("Testing column mapping logic...")
        
        # Create test data with actual Google Ads column names
        test_data = {
            'Date': ['2025-02-20', '2025-02-21'],
            'Campaign ID': ['12345', '67890'],
            'Campaign Name': ['Test Campaign 1', 'Test Campaign 2'],
            'Cost': [100.50, 200.75],
            'Impressions': [1000, 1500],
            'Clicks': [50, 75],
            'Conversions': [5, 8],
            'CTR': [0.05, 0.055],
            'CPC': [2.01, 2.68],
            'Conv. Rate': [0.10, 0.1067],
            'Cost per Conv.': [20.10, 25.09]
        }
        
        df = pd.DataFrame(test_data)
        print(f"✓ Test data created with columns: {list(df.columns)}")
        
        # Apply the column mapping
        column_mapping = {
            'Date': 'Day',
            'Campaign Name': 'Campaign', 
            'Impressions': 'Impr.',
            'Conv. Rate': 'Conv_Rate',
            'Cost per Conv.': 'Cost_Per_Conv'
        }
        
        df = df.rename(columns=column_mapping)
        print(f"✓ Columns mapped to: {list(df.columns)}")
        
        # Check required columns
        required_columns = ['Day', 'Campaign', 'Cost', 'Clicks', 'Impr.', 'Conversions']
        missing_required = [col for col in required_columns if col not in df.columns]
        
        if missing_required:
            print(f"✗ Missing required columns: {missing_required}")
            return False
        else:
            print("✓ All required columns present after mapping")
            
        # Test data type conversion
        df['Day'] = pd.to_datetime(df['Day'], errors='coerce')
        
        # Test numeric conversion
        numeric_columns = ['Cost', 'Clicks', 'Impr.', 'Conversions', 'CTR', 'CPC', 'Conv_Rate', 'Cost_Per_Conv']
        for col in numeric_columns:
            if col in df.columns:
                try:
                    if col in ['CTR', 'Conv_Rate'] and df[col].dtype == 'object':
                        df[col] = df[col].astype(str).str.replace('%', '').str.replace(',', '')
                        df[col] = pd.to_numeric(df[col], errors='coerce') / 100
                    else:
                        if df[col].dtype == 'object':
                            df[col] = df[col].astype(str).str.replace('$', '').str.replace(',', '')
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                except Exception as e:
                    print(f"Warning: Error converting column {col}: {e}")
                    df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print("✓ Data type conversion successful")
        
        # Verify the data looks correct
        print(f"✓ Final data shape: {df.shape}")
        print(f"✓ Sample data:")
        print(f"   Day: {df['Day'].iloc[0]}")
        print(f"   Campaign: {df['Campaign'].iloc[0]}")
        print(f"   Cost: {df['Cost'].iloc[0]}")
        print(f"   CTR: {df['CTR'].iloc[0]}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing Google Ads column mapping: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_google_ads_csv_loading():
    """Test loading actual Google Ads CSV file"""
    print("\n" + "=" * 60)
    print("TESTING GOOGLE ADS CSV LOADING")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        # Test that the application can load Google Ads CSV
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Switch to Google Ads mode
        app.data_source_var.set("google")
        app.switch_data_source()
        
        if app.current_data_source == "google":
            print("✓ Successfully switched to Google Ads mode")
        else:
            print("✗ Failed to switch to Google Ads mode")
            return False
        
        # Test the CSV loading method directly
        if os.path.exists('../gads.csv'):
            print("✓ Google Ads CSV file found")
            
            # Test loading
            gads_data = app.load_and_validate_gads_csv('../gads.csv')
            
            if gads_data is not None:
                print(f"✓ Google Ads CSV loaded successfully: {len(gads_data)} records")
                print(f"✓ Columns: {list(gads_data.columns)}")
                
                # Check that required columns exist
                required_columns = ['Day', 'Campaign', 'Cost', 'Clicks', 'Impr.', 'Conversions']
                missing = [col for col in required_columns if col not in gads_data.columns]
                
                if missing:
                    print(f"✗ Missing columns after loading: {missing}")
                    return False
                else:
                    print("✓ All required columns present after loading")
                
                # Check data types
                if pd.api.types.is_datetime64_any_dtype(gads_data['Day']):
                    print("✓ Day column properly converted to datetime")
                else:
                    print("✗ Day column not converted to datetime")
                    return False
                
                # Check numeric columns
                numeric_cols = ['Cost', 'Clicks', 'Impr.', 'Conversions']
                for col in numeric_cols:
                    if pd.api.types.is_numeric_dtype(gads_data[col]):
                        print(f"✓ {col} column properly converted to numeric")
                    else:
                        print(f"✗ {col} column not converted to numeric")
                        return False
                
                # Show sample data
                print(f"\n📊 Sample Google Ads data:")
                print(f"   Date range: {gads_data['Day'].min()} to {gads_data['Day'].max()}")
                print(f"   Total cost: ${gads_data['Cost'].sum():,.2f}")
                print(f"   Total clicks: {gads_data['Clicks'].sum():,.0f}")
                print(f"   Total impressions: {gads_data['Impr.'].sum():,.0f}")
                print(f"   Total conversions: {gads_data['Conversions'].sum():,.0f}")
                print(f"   Unique campaigns: {gads_data['Campaign'].nunique()}")
                
                return True
            else:
                print("✗ Failed to load Google Ads CSV")
                return False
        else:
            print("✗ Google Ads CSV file not found")
            return False
        
        root.destroy()
        
    except Exception as e:
        print(f"✗ Error testing Google Ads CSV loading: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_google_ads_processing():
    """Test Google Ads data processing"""
    print("\n" + "=" * 60)
    print("TESTING GOOGLE ADS DATA PROCESSING")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Switch to Google Ads and load data
        app.data_source_var.set("google")
        app.switch_data_source()
        
        if os.path.exists('../gads.csv'):
            app.gads_raw_data = app.load_and_validate_gads_csv('../gads.csv')
            
            if app.gads_raw_data is not None:
                print("✓ Google Ads data loaded for processing test")
                
                # Test processing
                app.process_google_ads_data()
                print("✓ Google Ads data processing completed without errors")
                
                # Test quality report generation
                app.generate_gads_quality_report()
                print("✓ Google Ads quality report generated")
                
                # Test aggregations
                if app.gads_monthly_summary is not None:
                    print(f"✓ Monthly summary generated: {len(app.gads_monthly_summary)} periods")
                
                if app.gads_daily_aggregated is not None:
                    print(f"✓ Daily aggregation generated: {len(app.gads_daily_aggregated)} days")
                
                return True
            else:
                print("✗ Failed to load Google Ads data for processing")
                return False
        else:
            print("✗ Google Ads CSV file not found")
            return False
        
        root.destroy()
        
    except Exception as e:
        print(f"✗ Error testing Google Ads processing: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Google Ads tests"""
    print("🔧 GOOGLE ADS FIX VALIDATION")
    print("=" * 80)
    print("Testing the Google Ads CSV loading and processing fixes:")
    print("• Column mapping from actual Google Ads export format")
    print("• CSV loading with proper column names")
    print("• Data processing and aggregation")
    print("=" * 80)
    
    # Run tests
    mapping_success = test_google_ads_column_mapping()
    loading_success = test_google_ads_csv_loading()
    processing_success = test_google_ads_processing()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 GOOGLE ADS FIX SUMMARY")
    print("=" * 80)
    
    print(f"Column Mapping: {'✅ PASS' if mapping_success else '❌ FAIL'}")
    print(f"CSV Loading: {'✅ PASS' if loading_success else '❌ FAIL'}")
    print(f"Data Processing: {'✅ PASS' if processing_success else '❌ FAIL'}")
    
    all_success = mapping_success and loading_success and processing_success
    
    if all_success:
        print(f"\n🎉 GOOGLE ADS FUNCTIONALITY FULLY FIXED!")
        print(f"   ✅ Column mapping handles actual Google Ads export format")
        print(f"   ✅ CSV loading works with your gads.csv file")
        print(f"   ✅ Data processing and aggregation functional")
        print(f"   ✅ Quality reporting and validation working")
        print(f"\n🚀 Google Ads is now fully functional in the application!")
    else:
        print(f"\n❌ Some Google Ads functionality needs attention.")
        
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
