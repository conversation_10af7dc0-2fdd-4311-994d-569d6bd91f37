#!/usr/bin/env python3
"""
Debug script to check what files are available in the deployment
"""
import os
import sys

def main():
    print("🔍 DEBUG: Checking deployment environment...")
    print(f"Python version: {sys.version}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    
    print("\n📁 Files in current directory:")
    try:
        files = os.listdir('.')
        for file in sorted(files):
            if os.path.isfile(file):
                size = os.path.getsize(file)
                print(f"  📄 {file} ({size} bytes)")
            else:
                print(f"  📁 {file}/")
    except Exception as e:
        print(f"❌ Error listing files: {e}")
    
    print("\n🔍 Checking for specific files:")
    required_files = ['config.py', 'server.py', 'start_server.py', 'requirements.txt', 'index.html']
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  ✅ {file} exists ({size} bytes)")
        else:
            print(f"  ❌ {file} missing")
    
    print("\n🐍 Testing config import:")
    try:
        import config
        print("  ✅ config module imported successfully")
        print(f"  📋 config module file: {config.__file__}")
        
        from config import AppConfig
        print("  ✅ AppConfig imported successfully")
        print(f"  🔧 AppConfig type: {type(AppConfig)}")
    except Exception as e:
        print(f"  ❌ Error importing config: {e}")
        print(f"  📍 Error type: {type(e).__name__}")
    
    print("\n🌐 Environment variables:")
    env_vars = ['CLAUDE_API_KEY', 'AIRTABLE_API_KEY', 'FLASK_ENV', 'PORT']
    for var in env_vars:
        value = os.getenv(var)
        if value:
            # Hide sensitive values
            if 'KEY' in var:
                display_value = value[:10] + "..." if len(value) > 10 else value
            else:
                display_value = value
            print(f"  ✅ {var} = {display_value}")
        else:
            print(f"  ❌ {var} not set")

if __name__ == "__main__":
    main()
