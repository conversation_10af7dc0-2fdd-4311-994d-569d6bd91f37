#!/usr/bin/env python3
"""
Test the three specific improvements to Meta Ads Transformer Complete:
1. Fix Google Ads Raw Data Filtering
2. Relocate Process & Validate Data Button
3. Add Data Summary Totals
"""

import sys
import os
sys.path.append('..')

def test_google_ads_raw_data_filtering():
    """Test that Google Ads raw data display uses filtered data"""
    print("🔍 TESTING GOOGLE ADS RAW DATA FILTERING FIX")
    print("=" * 60)
    
    try:
        import tkinter as tk
        import pandas as pd
        from datetime import datetime, timedelta
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Create test data
        test_dates = pd.date_range(start='2025-02-20', end='2025-03-21', freq='D')
        test_data = pd.DataFrame({
            'Day': test_dates,
            'Campaign': ['Test Campaign'] * len(test_dates),
            'Cost': [100.0] * len(test_dates),
            'Clicks': [50] * len(test_dates),
            'Impr.': [1000] * len(test_dates),
            'Conversions': [5] * len(test_dates),
            'CTR': [0.05] * len(test_dates),
            'CPC': [2.0] * len(test_dates)
        })
        
        app.gads_raw_data = test_data
        app.current_data_source = "google"
        
        print(f"✓ Test data created: {len(test_data)} records")
        
        # Check that update_gads_raw_display method exists and uses filtered data
        if hasattr(app, 'update_gads_raw_display'):
            print("✓ update_gads_raw_display method exists")
            
            # Check if method calls get_filtered_data
            import inspect
            source = inspect.getsource(app.update_gads_raw_display)
            if 'get_filtered_data' in source:
                print("✓ Raw data display method uses filtered data")
            else:
                print("✗ Raw data display method does not use filtered data")
                return False
                
            if 'filtered_data = self.get_filtered_data("google")' in source:
                print("✓ Correctly calls get_filtered_data for Google Ads")
            else:
                print("✗ Does not correctly call get_filtered_data for Google Ads")
                return False
                
        else:
            print("✗ update_gads_raw_display method missing")
            return False
        
        root.destroy()
        
        print(f"\n🎉 GOOGLE ADS RAW DATA FILTERING FIX VERIFIED!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing Google Ads raw data filtering: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_process_button_relocation():
    """Test that Process & Validate Data button is relocated correctly"""
    print("\n" + "=" * 60)
    print("TESTING PROCESS BUTTON RELOCATION")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Check that validate button exists
        if hasattr(app, 'validate_btn'):
            print("✓ Process & Validate Data button exists")
            
            # Check button properties
            button_text = app.validate_btn.cget('text')
            if "Process & Validate Data" in button_text:
                print("✓ Button has correct text")
            else:
                print(f"✗ Button text incorrect: {button_text}")
                return False
                
            # Check that button is initially disabled
            button_state = app.validate_btn.cget('state')
            if button_state == 'disabled':
                print("✓ Button is initially disabled (correct behavior)")
            else:
                print(f"⚠ Button state: {button_state} (should be disabled initially)")
                
        else:
            print("✗ Process & Validate Data button missing")
            return False
        
        # Check that load button exists (should be adjacent)
        if hasattr(app, 'load_btn'):
            print("✓ Load Data button exists")
        else:
            print("✗ Load Data button missing")
            return False
        
        root.destroy()
        
        print(f"\n🎉 PROCESS BUTTON RELOCATION VERIFIED!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing process button relocation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_summary_totals():
    """Test that data summary totals are implemented"""
    print("\n" + "=" * 60)
    print("TESTING DATA SUMMARY TOTALS")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Check for summary totals methods
        summary_methods = [
            'update_gads_summary_totals',
            'clear_gads_summary_totals'
        ]
        
        for method in summary_methods:
            if hasattr(app, method):
                print(f"✓ {method} method exists")
            else:
                print(f"✗ {method} method missing")
                return False
        
        # Check for summary labels attributes
        summary_attributes = [
            'gads_summary_labels',
            'gads_campaign_summary_labels', 
            'gads_monthly_summary_labels'
        ]
        
        # These will be created during setup, so we check if they're referenced in the code
        import inspect
        
        # Check update_gads_summary_totals method
        if hasattr(app, 'update_gads_summary_totals'):
            source = inspect.getsource(app.update_gads_summary_totals)
            
            for attr in summary_attributes:
                if attr in source:
                    print(f"✓ {attr} is used in summary totals method")
                else:
                    print(f"✗ {attr} not found in summary totals method")
                    return False
        
        # Check that summary totals are called from display methods
        if hasattr(app, 'update_gads_raw_display'):
            raw_source = inspect.getsource(app.update_gads_raw_display)
            if 'update_gads_summary_totals' in raw_source:
                print("✓ Raw data display calls summary totals update")
            else:
                print("✗ Raw data display does not call summary totals update")
                return False
        
        root.destroy()
        
        print(f"\n🎉 DATA SUMMARY TOTALS IMPLEMENTATION VERIFIED!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing data summary totals: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_functionality():
    """Test that all improvements work together"""
    print("\n" + "=" * 60)
    print("TESTING INTEGRATION FUNCTIONALITY")
    print("=" * 60)
    
    try:
        import tkinter as tk
        import pandas as pd
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Test that Google Ads tabs are set up correctly
        app.current_data_source = "google"
        app.setup_all_data_tabs()
        
        # Check for Google Ads specific tabs
        google_tabs = [
            'gads_campaign_tree',
            'gads_monthly_tree', 
            'gads_raw_tree',
            'gads_validation_tree',
            'gads_deep_dive_tree',
            'gads_temporal_tree'
        ]
        
        for tab in google_tabs:
            if hasattr(app, tab):
                print(f"✓ {tab} exists")
            else:
                print(f"✗ {tab} missing")
                return False
        
        # Test date filtering integration
        if hasattr(app, 'apply_period_filter'):
            print("✓ Date filtering functionality available")
        else:
            print("✗ Date filtering functionality missing")
            return False
        
        # Test that all display update methods exist
        display_methods = [
            'update_gads_displays',
            'update_gads_campaign_display',
            'update_gads_monthly_display',
            'update_gads_raw_display',
            'update_gads_validation_display',
            'update_gads_deep_dive_display',
            'update_gads_temporal_display'
        ]
        
        for method in display_methods:
            if hasattr(app, method):
                print(f"✓ {method} exists")
            else:
                print(f"✗ {method} missing")
                return False
        
        root.destroy()
        
        print(f"\n🎉 INTEGRATION FUNCTIONALITY VERIFIED!")
        return True
        
    except Exception as e:
        print(f"✗ Error testing integration functionality: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all improvement verification tests"""
    print("🔧 META ADS TRANSFORMER IMPROVEMENTS VERIFICATION")
    print("=" * 80)
    print("Testing three specific improvements:")
    print("1. Fix Google Ads Raw Data Filtering Issue")
    print("2. Relocate Process & Validate Data Button")
    print("3. Add Data Summary Totals")
    print("=" * 80)
    
    # Run all tests
    filtering_success = test_google_ads_raw_data_filtering()
    button_success = test_process_button_relocation()
    totals_success = test_data_summary_totals()
    integration_success = test_integration_functionality()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 IMPROVEMENTS VERIFICATION SUMMARY")
    print("=" * 80)
    
    print(f"Google Ads Raw Data Filtering: {'✅ PASS' if filtering_success else '❌ FAIL'}")
    print(f"Process Button Relocation: {'✅ PASS' if button_success else '❌ FAIL'}")
    print(f"Data Summary Totals: {'✅ PASS' if totals_success else '❌ FAIL'}")
    print(f"Integration Functionality: {'✅ PASS' if integration_success else '❌ FAIL'}")
    
    all_success = filtering_success and button_success and totals_success and integration_success
    
    if all_success:
        print(f"\n🎉 ALL IMPROVEMENTS SUCCESSFULLY IMPLEMENTED!")
        print(f"   ✅ Google Ads raw data now filters correctly with date filters")
        print(f"   ✅ Process & Validate Data button relocated for better UX")
        print(f"   ✅ Data summary totals implemented with automatic updates")
        print(f"   ✅ All improvements integrate seamlessly with existing functionality")
        print(f"\n🚀 Meta Ads Transformer Complete is ready for professional use!")
    else:
        print(f"\n❌ Some improvements need attention.")
        
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
