#!/usr/bin/env python3
"""
Test the restored original sophisticated Meta Ads interface
"""

import sys
import os
sys.path.append('..')

def test_original_interface():
    """Test that the original interface can be instantiated"""
    print("🔍 TESTING ORIGINAL SOPHISTICATED INTERFACE")
    print("=" * 60)
    
    try:
        # Test imports
        print("Testing imports...")
        import tkinter as tk
        from meta_ads_transformer_original import MetaAdsTransformerOriginal
        print("✓ All imports successful")
        
        # Test class instantiation
        print("\nTesting class instantiation...")
        root = tk.Tk()
        root.withdraw()  # Hide window for testing
        
        app = MetaAdsTransformerOriginal(root)
        print("✓ Original interface instantiated successfully")
        
        # Test that all tabs exist
        print("\nTesting interface components...")
        
        # Check main notebook exists
        if hasattr(app, 'main_notebook'):
            print("✓ Main notebook created")
        else:
            print("✗ Main notebook missing")
            return False
            
        # Check data notebook exists
        if hasattr(app, 'data_notebook'):
            print("✓ Data notebook created")
        else:
            print("✗ Data notebook missing")
            return False
            
        # Check all tree views exist
        tree_views = [
            'insights_tree', 'airtable_tree', 'summary_tree', 
            'monthly_tree', 'daily_tree', 'cleaned_tree', 
            'raw_tree', 'quality_text', 'quality_report_text'
        ]
        
        missing_components = []
        for component in tree_views:
            if hasattr(app, component):
                print(f"✓ {component} created")
            else:
                print(f"✗ {component} missing")
                missing_components.append(component)
        
        # Check processing options
        options = [
            'advanced_data_sourcing', 'daily_aggregation', 
            'monthly_summary_var', 'airtable_export_format', 
            'summary_row_validation'
        ]
        
        for option in options:
            if hasattr(app, option):
                print(f"✓ {option} option available")
            else:
                print(f"✗ {option} option missing")
                missing_components.append(option)
        
        # Check methods exist
        methods = [
            'load_csv', 'validate_data', 'process_all_data',
            'generate_ad_insights', 'export_airtable', 'export_ad_performance'
        ]
        
        for method in methods:
            if hasattr(app, method):
                print(f"✓ {method} method available")
            else:
                print(f"✗ {method} method missing")
                missing_components.append(method)
        
        root.destroy()
        
        if missing_components:
            print(f"\n❌ Missing components: {missing_components}")
            return False
        else:
            print(f"\n🎉 ALL COMPONENTS SUCCESSFULLY CREATED!")
            return True
            
    except Exception as e:
        print(f"✗ Error testing interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading_capability():
    """Test data loading capability"""
    print("\n" + "=" * 60)
    print("TESTING DATA LOADING CAPABILITY")
    print("=" * 60)
    
    try:
        import pandas as pd
        
        # Check if Meta Ads file exists
        if os.path.exists('../meta ads full.csv'):
            print("✓ Meta Ads CSV file found")
            
            # Test loading
            df = pd.read_csv('../meta ads full.csv')
            if len(df) > 1:
                df = df.iloc[1:].copy()  # Skip summary row
            
            df.columns = [c.strip() for c in df.columns]
            print(f"✓ CSV loaded: {len(df)} records, {len(df.columns)} columns")
            
            # Check for key columns
            key_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
            missing = [col for col in key_columns if col not in df.columns]
            
            if missing:
                print(f"✗ Missing key columns: {missing}")
                return False
            else:
                print("✓ All key columns present")
                
            # Show available columns
            print(f"\n📋 Available columns ({len(df.columns)}):")
            for i, col in enumerate(df.columns, 1):
                print(f"   {i:2d}. {col}")
                
            return True
            
        else:
            print("✗ Meta Ads CSV file not found")
            return False
            
    except Exception as e:
        print(f"✗ Error testing data loading: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 ORIGINAL SOPHISTICATED INTERFACE VALIDATION")
    print("=" * 80)
    print("Testing the restored original Meta Ads Transformer interface")
    print("with all sophisticated features and professional layout.")
    print("=" * 80)
    
    # Test interface
    interface_success = test_original_interface()
    
    # Test data loading
    data_success = test_data_loading_capability()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎯 VALIDATION SUMMARY")
    print("=" * 80)
    
    print(f"Interface Components: {'✅ PASS' if interface_success else '❌ FAIL'}")
    print(f"Data Loading Capability: {'✅ PASS' if data_success else '❌ FAIL'}")
    
    if interface_success and data_success:
        print(f"\n🎉 SUCCESS: Original sophisticated interface fully restored!")
        print(f"   • Professional layout with left control panel")
        print(f"   • Multiple detailed tabs for comprehensive data analysis")
        print(f"   • Advanced processing options and validation methodology")
        print(f"   • Rich data displays with all 21 Meta Ads columns")
        print(f"   • Comprehensive export options")
        print(f"   • Quality reporting and validation features")
        print(f"\n🚀 Ready to launch: python meta_ads_transformer_original.py")
    else:
        print(f"\n❌ Some components need attention.")
        
    return interface_success and data_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
