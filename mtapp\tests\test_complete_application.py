#!/usr/bin/env python3
"""
Test the complete Meta Ads Transformer with both Meta Ads and Google Ads support
"""

import sys
import os
sys.path.append('..')

def test_complete_application():
    """Test the complete application functionality"""
    print("🚀 TESTING COMPLETE META ADS TRANSFORMER")
    print("=" * 80)
    
    try:
        # Test imports
        print("Testing imports...")
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        print("✓ All imports successful")
        
        # Test class instantiation
        print("\nTesting class instantiation...")
        root = tk.Tk()
        root.withdraw()  # Hide window for testing
        
        app = MetaAdsTransformerComplete(root)
        print("✓ Complete application instantiated successfully")
        
        # Test data source switching
        print("\nTesting data source functionality...")
        
        # Check initial state
        if hasattr(app, 'current_data_source'):
            print(f"✓ Initial data source: {app.current_data_source}")
        else:
            print("✗ Data source tracking missing")
            return False
            
        # Test switching to Google Ads
        app.data_source_var.set("google")
        app.switch_data_source()
        if app.current_data_source == "google":
            print("✓ Successfully switched to Google Ads")
        else:
            print("✗ Failed to switch to Google Ads")
            return False
            
        # Test switching back to Meta Ads
        app.data_source_var.set("meta")
        app.switch_data_source()
        if app.current_data_source == "meta":
            print("✓ Successfully switched back to Meta Ads")
        else:
            print("✗ Failed to switch back to Meta Ads")
            return False
        
        # Test interface components
        print("\nTesting interface components...")
        
        # Check main components
        components = [
            'main_notebook', 'data_notebook', 'quality_text',
            'validate_btn', 'load_btn', 'file_status_label'
        ]
        
        missing_components = []
        for component in components:
            if hasattr(app, component):
                print(f"✓ {component} exists")
            else:
                print(f"✗ {component} missing")
                missing_components.append(component)
        
        # Check processing options
        options = [
            'advanced_data_sourcing', 'daily_aggregation', 
            'monthly_summary_var', 'airtable_export_format', 
            'summary_row_validation'
        ]
        
        for option in options:
            if hasattr(app, option):
                print(f"✓ {option} option available")
            else:
                print(f"✗ {option} option missing")
                missing_components.append(option)
        
        # Check methods exist
        methods = [
            'load_csv', 'process_and_validate', 'load_and_validate_meta_csv',
            'load_and_validate_gads_csv', 'generate_meta_quality_report',
            'generate_gads_quality_report', 'export_airtable', 'export_validation_report'
        ]
        
        for method in methods:
            if hasattr(app, method):
                print(f"✓ {method} method available")
            else:
                print(f"✗ {method} method missing")
                missing_components.append(method)
        
        root.destroy()
        
        if missing_components:
            print(f"\n❌ Missing components: {missing_components}")
            return False
        else:
            print(f"\n🎉 ALL COMPONENTS SUCCESSFULLY CREATED!")
            return True
            
    except Exception as e:
        print(f"✗ Error testing complete application: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading_capability():
    """Test data loading capability for both sources"""
    print("\n" + "=" * 80)
    print("TESTING DUAL DATA SOURCE CAPABILITY")
    print("=" * 80)
    
    try:
        import pandas as pd
        
        # Test Meta Ads file
        meta_success = False
        if os.path.exists('../meta ads full.csv'):
            print("✓ Meta Ads CSV file found")
            
            df = pd.read_csv('../meta ads full.csv')
            if len(df) > 1:
                df = df.iloc[1:].copy()
            
            df.columns = [c.strip() for c in df.columns]
            print(f"✓ Meta Ads CSV loaded: {len(df)} records, {len(df.columns)} columns")
            
            # Check for key Meta Ads columns
            meta_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
            missing = [col for col in meta_columns if col not in df.columns]
            
            if missing:
                print(f"✗ Missing Meta Ads columns: {missing}")
            else:
                print("✓ All key Meta Ads columns present")
                meta_success = True
        else:
            print("✗ Meta Ads CSV file not found")
        
        # Test Google Ads file
        gads_success = False
        if os.path.exists('../gads.csv'):
            print("✓ Google Ads CSV file found")
            
            df = pd.read_csv('../gads.csv')
            df.columns = [c.strip() for c in df.columns]
            print(f"✓ Google Ads CSV loaded: {len(df)} records, {len(df.columns)} columns")
            
            # Check for key Google Ads columns
            gads_columns = ['Day', 'Campaign', 'Cost', 'Clicks', 'Impr.', 'Conversions']
            missing = [col for col in gads_columns if col not in df.columns]
            
            if missing:
                print(f"✗ Missing Google Ads columns: {missing}")
            else:
                print("✓ All key Google Ads columns present")
                gads_success = True
        else:
            print("✗ Google Ads CSV file not found")
        
        return meta_success and gads_success
        
    except Exception as e:
        print(f"✗ Error testing data loading: {e}")
        return False

def test_fixed_issues():
    """Test that the reported issues are fixed"""
    print("\n" + "=" * 80)
    print("TESTING FIXED ISSUES")
    print("=" * 80)
    
    try:
        # Test 1: Pandas FutureWarning fix
        print("Testing pandas datetime parsing fix...")
        import pandas as pd
        
        # Create test data with mixed timezones (this would trigger the warning)
        test_dates = ['2024-01-01', '2024-01-02 10:00:00+00:00', '2024-01-03']
        test_df = pd.DataFrame({'dates': test_dates})
        
        # Test the fixed parsing method
        try:
            parsed_dates = pd.to_datetime(test_df['dates'], errors='coerce', utc=True).dt.tz_localize(None)
            print("✓ Pandas datetime parsing fixed - no FutureWarning")
        except Exception as e:
            print(f"✗ Pandas datetime parsing issue: {e}")
            return False
        
        # Test 2: Process validation button functionality
        print("\nTesting process validation button...")
        import tkinter as tk
        from meta_ads_transformer_complete import MetaAdsTransformerComplete
        
        root = tk.Tk()
        root.withdraw()
        app = MetaAdsTransformerComplete(root)
        
        # Check that the validate button exists and has the correct command
        if hasattr(app, 'validate_btn') and hasattr(app, 'process_and_validate'):
            print("✓ Process validation button exists with correct functionality")
        else:
            print("✗ Process validation button missing or incorrect")
            root.destroy()
            return False
        
        # Test 3: Google Ads integration
        print("\nTesting Google Ads integration...")
        
        # Check Google Ads specific methods
        gads_methods = [
            'load_and_validate_gads_csv', 'process_google_ads_data',
            'generate_gads_quality_report', 'aggregate_gads_by_period'
        ]
        
        missing_gads = []
        for method in gads_methods:
            if hasattr(app, method):
                print(f"✓ {method} available")
            else:
                print(f"✗ {method} missing")
                missing_gads.append(method)
        
        root.destroy()
        
        if missing_gads:
            print(f"✗ Missing Google Ads methods: {missing_gads}")
            return False
        else:
            print("✓ Google Ads integration complete")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing fixed issues: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🔍 COMPLETE APPLICATION VALIDATION")
    print("=" * 100)
    print("Testing the complete Meta Ads Transformer with:")
    print("• Fixed pandas FutureWarning")
    print("• Working process validation button")
    print("• Full Google Ads integration")
    print("• Original sophisticated interface")
    print("=" * 100)
    
    # Run tests
    app_success = test_complete_application()
    data_success = test_data_loading_capability()
    fixes_success = test_fixed_issues()
    
    # Summary
    print("\n" + "=" * 100)
    print("🎯 VALIDATION SUMMARY")
    print("=" * 100)
    
    print(f"Complete Application: {'✅ PASS' if app_success else '❌ FAIL'}")
    print(f"Dual Data Source Support: {'✅ PASS' if data_success else '❌ FAIL'}")
    print(f"Issue Fixes: {'✅ PASS' if fixes_success else '❌ FAIL'}")
    
    if app_success and data_success and fixes_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   • Pandas FutureWarning fixed")
        print(f"   • Process validation button working")
        print(f"   • Google Ads integration complete")
        print(f"   • Original sophisticated interface restored")
        print(f"   • Dual data source support functional")
        print(f"\n🚀 Ready to launch: python meta_ads_transformer_complete.py")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
        
    return app_success and data_success and fixes_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
