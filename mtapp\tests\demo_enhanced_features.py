#!/usr/bin/env python3
"""
Demonstration of Enhanced Ads Transformer Features
Shows the key capabilities for both Meta Ads and Google Ads processing
"""

import pandas as pd
import numpy as np
from datetime import datetime
import sys
import os

def demo_meta_ads_analysis():
    """Demonstrate Meta Ads analysis capabilities"""
    print("📊 META ADS ANALYSIS DEMONSTRATION")
    print("=" * 60)
    
    try:
        # Load Meta Ads data
        df = pd.read_csv('meta ads full.csv')
        
        # Clean and process data
        df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce')
        df['Amount spent (USD)'] = pd.to_numeric(df['Amount spent (USD)'], errors='coerce')
        df['Results'] = pd.to_numeric(df['Results'], errors='coerce')
        df['Reach'] = pd.to_numeric(df['Reach'], errors='coerce')
        df['Impressions'] = pd.to_numeric(df['Impressions'], errors='coerce')
        
        # Remove invalid data
        df = df[df['Reporting ends'].notnull()].copy()
        
        print(f"📈 Data Overview:")
        print(f"   • Total Records: {len(df):,}")
        print(f"   • Date Range: {df['Reporting ends'].min().strftime('%Y-%m-%d')} to {df['Reporting ends'].max().strftime('%Y-%m-%d')}")
        print(f"   • Total Spend: ${df['Amount spent (USD)'].sum():,.2f}")
        print(f"   • Total Results: {df['Results'].sum():,.0f}")
        print(f"   • Total Reach: {df['Reach'].sum():,.0f}")
        
        # Monthly aggregation
        monthly = df.groupby(df['Reporting ends'].dt.to_period('M')).agg({
            'Amount spent (USD)': 'sum',
            'Results': 'sum',
            'Reach': 'sum',
            'Impressions': 'sum'
        })
        
        print(f"\n📅 Monthly Performance:")
        for period, row in monthly.iterrows():
            frequency = row['Impressions'] / row['Reach'] if row['Reach'] > 0 else 0
            print(f"   • {period}: ${row['Amount spent (USD)']:,.2f} spend, {row['Results']:,.0f} results, {frequency:.2f} frequency")
        
        return True
        
    except Exception as e:
        print(f"❌ Meta Ads demo failed: {e}")
        return False

def demo_google_ads_analysis():
    """Demonstrate Google Ads analysis capabilities"""
    print("\n🔍 GOOGLE ADS ANALYSIS DEMONSTRATION")
    print("=" * 60)
    
    try:
        # Load Google Ads data
        df = pd.read_csv('gads.csv')
        
        # Clean and process data
        df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
        numeric_columns = ['Cost', 'Impressions', 'Clicks', 'Conversions']
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove invalid data
        df = df[df['Date'].notnull()].copy()
        
        print(f"📈 Data Overview:")
        print(f"   • Total Records: {len(df):,}")
        print(f"   • Date Range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}")
        print(f"   • Unique Campaigns: {df['Campaign ID'].nunique():,}")
        print(f"   • Total Cost: ${df['Cost'].sum():,.2f}")
        print(f"   • Total Conversions: {df['Conversions'].sum():,.0f}")
        
        # Calculate key metrics
        total_clicks = df['Clicks'].sum()
        total_impressions = df['Impressions'].sum()
        total_cost = df['Cost'].sum()
        total_conversions = df['Conversions'].sum()
        
        avg_ctr = (total_clicks / total_impressions * 100) if total_impressions > 0 else 0
        avg_cpc = (total_cost / total_clicks) if total_clicks > 0 else 0
        conv_rate = (total_conversions / total_clicks * 100) if total_clicks > 0 else 0
        
        print(f"\n📊 Performance Metrics:")
        print(f"   • Average CTR: {avg_ctr:.2f}%")
        print(f"   • Average CPC: ${avg_cpc:.2f}")
        print(f"   • Conversion Rate: {conv_rate:.2f}%")
        print(f"   • Cost per Conversion: ${(total_cost/total_conversions):.2f}" if total_conversions > 0 else "   • Cost per Conversion: N/A")
        
        # Top performing campaigns
        campaigns = df.groupby('Campaign Name').agg({
            'Cost': 'sum',
            'Conversions': 'sum',
            'Clicks': 'sum'
        }).sort_values('Conversions', ascending=False)
        
        print(f"\n🏆 Top 5 Campaigns by Conversions:")
        for i, (campaign, data) in enumerate(campaigns.head(5).iterrows(), 1):
            roi_score = data['Conversions'] / data['Cost'] if data['Cost'] > 0 else 0
            print(f"   {i}. {campaign[:40]}")
            print(f"      └─ {data['Conversions']:.0f} conversions, ${data['Cost']:.2f} cost, {roi_score:.3f} ROI")
        
        # Monthly trends
        monthly = df.groupby(df['Date'].dt.to_period('M')).agg({
            'Cost': 'sum',
            'Conversions': 'sum',
            'Clicks': 'sum'
        })
        
        print(f"\n📅 Monthly Trends:")
        for period, row in monthly.iterrows():
            monthly_cpc = row['Cost'] / row['Clicks'] if row['Clicks'] > 0 else 0
            print(f"   • {period}: ${row['Cost']:,.2f} cost, {row['Conversions']:.0f} conversions, ${monthly_cpc:.2f} CPC")
        
        return True
        
    except Exception as e:
        print(f"❌ Google Ads demo failed: {e}")
        return False

def demo_comparison_analysis():
    """Demonstrate comparison between Meta Ads and Google Ads"""
    print("\n⚖️  PLATFORM COMPARISON ANALYSIS")
    print("=" * 60)
    
    try:
        # Load both datasets
        meta_df = pd.read_csv('meta ads full.csv')
        google_df = pd.read_csv('gads.csv')
        
        # Process Meta Ads data
        meta_df['Amount spent (USD)'] = pd.to_numeric(meta_df['Amount spent (USD)'], errors='coerce')
        meta_df['Results'] = pd.to_numeric(meta_df['Results'], errors='coerce')
        meta_total_spend = meta_df['Amount spent (USD)'].sum()
        meta_total_results = meta_df['Results'].sum()
        
        # Process Google Ads data
        google_df['Cost'] = pd.to_numeric(google_df['Cost'], errors='coerce')
        google_df['Conversions'] = pd.to_numeric(google_df['Conversions'], errors='coerce')
        google_total_spend = google_df['Cost'].sum()
        google_total_conversions = google_df['Conversions'].sum()
        
        print(f"💰 Spend Comparison:")
        print(f"   • Meta Ads Total Spend: ${meta_total_spend:,.2f}")
        print(f"   • Google Ads Total Spend: ${google_total_spend:,.2f}")
        print(f"   • Combined Total Spend: ${meta_total_spend + google_total_spend:,.2f}")
        
        print(f"\n🎯 Results/Conversions Comparison:")
        print(f"   • Meta Ads Results: {meta_total_results:,.0f}")
        print(f"   • Google Ads Conversions: {google_total_conversions:,.0f}")
        print(f"   • Combined Results: {meta_total_results + google_total_conversions:,.0f}")
        
        print(f"\n📈 Cost Efficiency:")
        meta_cost_per_result = meta_total_spend / meta_total_results if meta_total_results > 0 else 0
        google_cost_per_conv = google_total_spend / google_total_conversions if google_total_conversions > 0 else 0
        
        print(f"   • Meta Ads Cost per Result: ${meta_cost_per_result:.2f}")
        print(f"   • Google Ads Cost per Conversion: ${google_cost_per_conv:.2f}")
        
        if meta_cost_per_result > 0 and google_cost_per_conv > 0:
            if meta_cost_per_result < google_cost_per_conv:
                print(f"   • Meta Ads is {((google_cost_per_conv/meta_cost_per_result-1)*100):.1f}% more cost-efficient")
            else:
                print(f"   • Google Ads is {((meta_cost_per_result/google_cost_per_conv-1)*100):.1f}% more cost-efficient")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison demo failed: {e}")
        return False

def main():
    """Run the complete demonstration"""
    print("🚀 ENHANCED ADS TRANSFORMER - FEATURE DEMONSTRATION")
    print("=" * 80)
    print("This demo showcases the enhanced capabilities for processing both")
    print("Meta Ads (Facebook) and Google Ads data with comprehensive analytics.")
    print("=" * 80)
    
    # Run demonstrations
    meta_success = demo_meta_ads_analysis()
    google_success = demo_google_ads_analysis()
    comparison_success = demo_comparison_analysis()
    
    # Summary
    print("\n" + "=" * 80)
    print("🎉 DEMONSTRATION COMPLETE")
    print("=" * 80)
    
    if meta_success and google_success and comparison_success:
        print("✅ All demonstrations completed successfully!")
        print("\n🔧 Enhanced Features Available:")
        print("   • Dual platform support (Meta Ads + Google Ads)")
        print("   • Advanced data validation and quality reporting")
        print("   • Comprehensive performance metrics calculation")
        print("   • Campaign-level analysis and ranking")
        print("   • Time-based aggregation (daily/monthly)")
        print("   • Cross-platform comparison analysis")
        print("   • Export functionality with proper file naming")
        print("   • User-friendly GUI with data source selection")
        
        print("\n📊 Ready for Production Use!")
        print("Launch the application with: python meta_ads_transformer_fixed.py")
    else:
        print("❌ Some demonstrations failed. Please check the data files.")

if __name__ == "__main__":
    main()
