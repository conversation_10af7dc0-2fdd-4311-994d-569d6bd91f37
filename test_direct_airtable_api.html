<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Airtable API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .result {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        .loading { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Direct Airtable API Test</h1>
        <p>This test bypasses all caching and directly tests the Airtable API pagination.</p>
        
        <h2>Test 1: No maxRecords Parameter (Should get ALL records)</h2>
        <button onclick="testNoMaxRecords()">🚀 Test No maxRecords</button>
        <div id="no-max-result"></div>

        <h2>Test 2: maxRecords=2000 (Should get ALL records)</h2>
        <button onclick="testMaxRecords2000()">📊 Test maxRecords=2000</button>
        <div id="max-2000-result"></div>

        <h2>Test 3: maxRecords=1000 (Should get only 1000 records)</h2>
        <button onclick="testMaxRecords1000()">⚠️ Test maxRecords=1000</button>
        <div id="max-1000-result"></div>

        <h2>Test 4: Check Server Environment</h2>
        <button onclick="checkServerEnvironment()">🔧 Check Environment</button>
        <div id="environment-result"></div>
    </div>

    <script>
        async function testNoMaxRecords() {
            const resultDiv = document.getElementById('no-max-result');
            resultDiv.innerHTML = '<div class="loading">🚀 Testing API without maxRecords parameter...</div>';
            
            try {
                console.log('🚀 Testing API without maxRecords parameter...');
                
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test without maxRecords parameter
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const recordCount = data.records ? data.records.length : 0;
                
                let result = `✅ No maxRecords Parameter Test:\n`;
                result += `📊 Total records: ${recordCount}\n`;
                result += `🔧 Response type: ${Array.isArray(data) ? 'Array' : 'Object'}\n`;
                
                if (data.pagination_info) {
                    result += `📄 Pages fetched: ${data.pagination_info.pages_fetched}\n`;
                    result += `🔄 Server-side pagination: ${data.pagination_info.server_side_pagination}\n`;
                    result += `🗑️ Duplicates removed: ${data.pagination_info.duplicates_removed}\n`;
                }
                
                if (recordCount === 1514) {
                    result += `\n🎯 SUCCESS: Got all 1,514 records without maxRecords!`;
                    resultDiv.className = 'result success';
                } else if (recordCount === 1000) {
                    result += `\n❌ PROBLEM: Only got 1,000 records (default limit applied)`;
                    resultDiv.className = 'result error';
                } else {
                    result += `\n❓ UNEXPECTED: Got ${recordCount} records`;
                    resultDiv.className = 'result info';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('No maxRecords test error:', error);
            }
        }

        async function testMaxRecords2000() {
            const resultDiv = document.getElementById('max-2000-result');
            resultDiv.innerHTML = '<div class="loading">📊 Testing API with maxRecords=2000...</div>';
            
            try {
                console.log('📊 Testing API with maxRecords=2000...');
                
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test with maxRecords=2000
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL&maxRecords=2000&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const recordCount = data.records ? data.records.length : 0;
                
                let result = `✅ maxRecords=2000 Test:\n`;
                result += `📊 Total records: ${recordCount}\n`;
                
                if (data.pagination_info) {
                    result += `📄 Pages fetched: ${data.pagination_info.pages_fetched}\n`;
                    result += `🔄 Server-side pagination: ${data.pagination_info.server_side_pagination}\n`;
                }
                
                if (recordCount === 1514) {
                    result += `\n🎯 SUCCESS: Got all 1,514 records with maxRecords=2000!`;
                    resultDiv.className = 'result success';
                } else if (recordCount === 1000) {
                    result += `\n❌ PROBLEM: Only got 1,000 records (server ignoring maxRecords)`;
                    resultDiv.className = 'result error';
                } else {
                    result += `\n❓ UNEXPECTED: Got ${recordCount} records`;
                    resultDiv.className = 'result info';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('maxRecords=2000 test error:', error);
            }
        }

        async function testMaxRecords1000() {
            const resultDiv = document.getElementById('max-1000-result');
            resultDiv.innerHTML = '<div class="loading">⚠️ Testing API with maxRecords=1000...</div>';
            
            try {
                console.log('⚠️ Testing API with maxRecords=1000...');
                
                // Clear cache first
                await fetch('/api/cache', { method: 'DELETE' });
                
                // Test with maxRecords=1000
                const timestamp = new Date().getTime();
                const response = await fetch(`/api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL&maxRecords=1000&_t=${timestamp}`);
                
                if (!response.ok) {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const recordCount = data.records ? data.records.length : 0;
                
                let result = `✅ maxRecords=1000 Test:\n`;
                result += `📊 Total records: ${recordCount}\n`;
                
                if (data.pagination_info) {
                    result += `📄 Pages fetched: ${data.pagination_info.pages_fetched}\n`;
                    result += `🔄 Server-side pagination: ${data.pagination_info.server_side_pagination}\n`;
                }
                
                if (recordCount === 1000) {
                    result += `\n✅ EXPECTED: Got exactly 1,000 records (limit working)`;
                    resultDiv.className = 'result success';
                } else if (recordCount === 1514) {
                    result += `\n❓ UNEXPECTED: Got all 1,514 records (limit ignored)`;
                    resultDiv.className = 'result info';
                } else {
                    result += `\n❓ UNEXPECTED: Got ${recordCount} records`;
                    resultDiv.className = 'result info';
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('maxRecords=1000 test error:', error);
            }
        }

        async function checkServerEnvironment() {
            const resultDiv = document.getElementById('environment-result');
            resultDiv.innerHTML = '<div class="loading">🔧 Checking server environment...</div>';
            
            try {
                console.log('🔧 Checking server environment...');
                
                // Check health endpoint for environment info
                const response = await fetch('/health');
                
                if (!response.ok) {
                    throw new Error(`Health check returned ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                let result = `✅ Server Environment Check:\n`;
                result += `📊 Status: ${data.status}\n`;
                result += `📅 Timestamp: ${data.timestamp}\n`;
                result += `🔧 Version: ${data.version}\n\n`;
                
                result += `Components:\n`;
                for (const [component, info] of Object.entries(data.components || {})) {
                    result += `  ${component}: ${info.status} - ${info.message}\n`;
                }
                
                // Check cache stats
                try {
                    const cacheResponse = await fetch('/api/cache');
                    if (cacheResponse.ok) {
                        const cacheData = await cacheResponse.json();
                        result += `\nCache Info:\n`;
                        result += `  Total entries: ${cacheData.total_entries}\n`;
                        result += `  Total size: ${cacheData.total_size_bytes} bytes\n`;
                    }
                } catch (e) {
                    result += `\nCache Info: Unable to fetch (${e.message})\n`;
                }
                
                resultDiv.className = 'result info';
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
                console.error('Environment check error:', error);
            }
        }
    </script>
</body>
</html>
