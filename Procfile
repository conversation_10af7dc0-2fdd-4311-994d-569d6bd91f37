web: python3 -c "import os,sys;from datetime import datetime;from flask import Flask,jsonify,send_from_directory;from flask_cors import CORS;CLAUDE_API_KEY=os.getenv('CLAUDE_API_KEY');AIRTABLE_API_KEY=os.getenv('AIRTABLE_API_KEY');print('❌ Missing API keys') if not CLAUDE_API_KEY or not AIRTABLE_API_KEY else print('✅ Environment variables loaded');app=Flask(__name__,static_folder='.',static_url_path='');CORS(app,origins=['*']);app.add_url_rule('/','index',lambda:send_from_directory('.','index.html'));app.add_url_rule('/health','health',lambda:jsonify({'status':'healthy','timestamp':datetime.now().isoformat()}));app.add_url_rule('/api/test','test',lambda:jsonify({'message':'RepairLift Dashboard API is working!','timestamp':datetime.now().isoformat()}));print('🚀 Starting RepairLift Dashboard...');app.run(host='0.0.0.0',port=int(os.getenv('PORT',8000)),debug=False)"
