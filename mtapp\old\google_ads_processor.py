"""
Google Ads Data Processor
Handles validation, aggregation, and analysis of Google Ads CSV data
"""

import pandas as pd
import numpy as np
from datetime import datetime
import traceback


class GoogleAdsProcessor:
    """
    Processes Google Ads CSV data with validation, aggregation, and quality reporting
    """
    
    def __init__(self):
        self.required_columns = [
            'Date', 'Campaign', 'Ad Group', 'Clicks', 'Impressions', 
            'CTR', 'Avg. CPC', 'Cost', 'Conversions', 'Conv. Rate', 
            'Cost / Conv.', 'Campaign Status', 'Campaign Type'
        ]
        self.numeric_columns = [
            'Clicks', 'Impressions', 'Cost', 'Conversions'
        ]
        self.percentage_columns = ['CTR', 'Conv. Rate']
        self.currency_columns = ['Avg. CPC', 'Cost / Conv.']
        
    def validate_csv(self, file_path):
        """
        Load and validate Google Ads CSV file
        
        Args:
            file_path (str): Path to the CSV file
            
        Returns:
            pandas.DataFrame or None: Validated dataframe or None if validation fails
        """
        try:
            # Load CSV
            df = pd.read_csv(file_path)
            
            # Clean column names
            df.columns = [c.strip() for c in df.columns]
            
            # Validate required columns
            missing_columns = [col for col in self.required_columns if col not in df.columns]
            
            if missing_columns:
                raise ValueError(f"Required columns missing: {', '.join(missing_columns)}")
            
            # Convert and validate data types
            df = self._convert_data_types(df)
            
            # Remove rows with invalid dates
            initial_count = len(df)
            df = df[df['Date'].notnull()].copy()
            
            if len(df) == 0:
                raise ValueError("No valid data found after cleaning")
            
            # Validate data consistency
            self._validate_data_consistency(df)
            
            return df
            
        except Exception as e:
            raise Exception(f"Error validating Google Ads CSV: {str(e)}")
    
    def _convert_data_types(self, df):
        """Convert columns to appropriate data types"""
        try:
            # Convert date column
            df['Date'] = pd.to_datetime(df['Date'], errors='coerce')
            
            # Convert numeric columns
            for col in self.numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Convert percentage columns (remove % and convert to float)
            for col in self.percentage_columns:
                if col in df.columns:
                    df[col] = df[col].astype(str).str.replace('%', '').astype(float, errors='ignore')
            
            # Convert currency columns (remove currency symbols)
            for col in self.currency_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except Exception as e:
            raise Exception(f"Error converting data types: {str(e)}")
    
    def _validate_data_consistency(self, df):
        """Validate data consistency and relationships"""
        try:
            # Check for negative values in key metrics
            for col in ['Clicks', 'Impressions', 'Cost', 'Conversions']:
                if (df[col] < 0).any():
                    raise ValueError(f"Negative values found in {col}")
            
            # Validate CTR calculation (within reasonable tolerance)
            calculated_ctr = np.where(df['Impressions'] > 0, 
                                    (df['Clicks'] / df['Impressions']) * 100, 0)
            ctr_diff = np.abs(calculated_ctr - df['CTR'])
            if (ctr_diff > 0.1).any():  # Allow 0.1% tolerance
                print("Warning: CTR values may not match calculated values")
            
            # Validate conversion rate calculation
            calculated_conv_rate = np.where(df['Clicks'] > 0,
                                          (df['Conversions'] / df['Clicks']) * 100, 0)
            conv_rate_diff = np.abs(calculated_conv_rate - df['Conv. Rate'])
            if (conv_rate_diff > 0.1).any():  # Allow 0.1% tolerance
                print("Warning: Conversion rate values may not match calculated values")
            
        except Exception as e:
            print(f"Warning: Data consistency validation failed: {str(e)}")
    
    def aggregate_by_period(self, df, period='D'):
        """
        Aggregate Google Ads data by time period
        
        Args:
            df (pandas.DataFrame): Input dataframe
            period (str): Aggregation period ('D' for daily, 'M' for monthly)
            
        Returns:
            pandas.DataFrame: Aggregated data
        """
        try:
            # Group by period and aggregate
            grouped = df.groupby(df['Date'].dt.to_period(period)).agg({
                'Clicks': 'sum',
                'Impressions': 'sum',
                'Cost': 'sum',
                'Conversions': 'sum',
                'Campaign': 'count'  # Count campaigns
            }).reset_index()
            
            # Rename columns
            grouped = grouped.rename(columns={
                'Date': 'Period',
                'Clicks': 'Total_Clicks',
                'Impressions': 'Total_Impressions',
                'Cost': 'Total_Cost',
                'Conversions': 'Total_Conversions',
                'Campaign': 'Campaign_Count'
            })
            
            # Calculate derived metrics
            grouped['Average_CTR'] = np.where(
                grouped['Total_Impressions'] > 0,
                (grouped['Total_Clicks'] / grouped['Total_Impressions']) * 100,
                0
            )
            
            grouped['Average_CPC'] = np.where(
                grouped['Total_Clicks'] > 0,
                grouped['Total_Cost'] / grouped['Total_Clicks'],
                0
            )
            
            grouped['Conversion_Rate'] = np.where(
                grouped['Total_Clicks'] > 0,
                (grouped['Total_Conversions'] / grouped['Total_Clicks']) * 100,
                0
            )
            
            grouped['Cost_Per_Conversion'] = np.where(
                grouped['Total_Conversions'] > 0,
                grouped['Total_Cost'] / grouped['Total_Conversions'],
                0
            )
            
            # Format period
            grouped['Period'] = grouped['Period'].astype(str)
            
            return grouped
            
        except Exception as e:
            raise Exception(f"Error in aggregation: {str(e)}")
    
    def calculate_campaign_performance(self, df):
        """
        Calculate campaign-level performance metrics
        
        Args:
            df (pandas.DataFrame): Input dataframe
            
        Returns:
            pandas.DataFrame: Campaign performance data
        """
        try:
            campaign_stats = df.groupby('Campaign').agg({
                'Clicks': 'sum',
                'Impressions': 'sum',
                'Cost': 'sum',
                'Conversions': 'sum',
                'Date': 'count'  # Count of days active
            }).reset_index()
            
            campaign_stats = campaign_stats.rename(columns={'Date': 'Days_Active'})
            
            # Calculate performance metrics
            campaign_stats['CTR'] = np.where(
                campaign_stats['Impressions'] > 0,
                (campaign_stats['Clicks'] / campaign_stats['Impressions']) * 100,
                0
            )
            
            campaign_stats['CPC'] = np.where(
                campaign_stats['Clicks'] > 0,
                campaign_stats['Cost'] / campaign_stats['Clicks'],
                0
            )
            
            campaign_stats['Conversion_Rate'] = np.where(
                campaign_stats['Clicks'] > 0,
                (campaign_stats['Conversions'] / campaign_stats['Clicks']) * 100,
                0
            )
            
            campaign_stats['Cost_Per_Conversion'] = np.where(
                campaign_stats['Conversions'] > 0,
                campaign_stats['Cost'] / campaign_stats['Conversions'],
                0
            )
            
            # Calculate ROI score (conversions per dollar spent)
            campaign_stats['ROI_Score'] = np.where(
                campaign_stats['Cost'] > 0,
                campaign_stats['Conversions'] / campaign_stats['Cost'],
                0
            )
            
            # Sort by ROI score descending
            campaign_stats = campaign_stats.sort_values('ROI_Score', ascending=False)
            
            return campaign_stats
            
        except Exception as e:
            raise Exception(f"Error calculating campaign performance: {str(e)}")
    
    def generate_quality_report(self, df):
        """
        Generate data quality report for Google Ads data
        
        Args:
            df (pandas.DataFrame): Input dataframe
            
        Returns:
            dict: Quality report data
        """
        try:
            report = {
                'total_records': len(df),
                'date_range': (df['Date'].min(), df['Date'].max()),
                'total_cost': df['Cost'].sum(),
                'total_clicks': df['Clicks'].sum(),
                'total_impressions': df['Impressions'].sum(),
                'total_conversions': df['Conversions'].sum(),
                'unique_campaigns': df['Campaign'].nunique(),
                'campaign_types': df['Campaign Type'].value_counts().to_dict(),
                'active_campaigns': len(df[df['Campaign Status'] == 'Enabled']['Campaign'].unique())
            }
            
            # Calculate overall metrics
            report['overall_ctr'] = (report['total_clicks'] / report['total_impressions'] * 100) if report['total_impressions'] > 0 else 0
            report['overall_cpc'] = (report['total_cost'] / report['total_clicks']) if report['total_clicks'] > 0 else 0
            report['overall_conversion_rate'] = (report['total_conversions'] / report['total_clicks'] * 100) if report['total_clicks'] > 0 else 0
            report['overall_cost_per_conversion'] = (report['total_cost'] / report['total_conversions']) if report['total_conversions'] > 0 else 0
            
            # Data completeness
            report['data_completeness'] = {}
            for col in self.numeric_columns:
                null_count = df[col].isnull().sum()
                null_pct = (null_count / len(df)) * 100
                report['data_completeness'][col] = {
                    'missing_count': null_count,
                    'missing_percentage': null_pct
                }
            
            return report

        except Exception as e:
            raise Exception(f"Error generating quality report: {str(e)}")

    def get_top_performing_campaigns(self, df, metric='Conversions', top_n=10):
        """
        Get top performing campaigns by specified metric

        Args:
            df (pandas.DataFrame): Input dataframe
            metric (str): Metric to rank by ('Conversions', 'CTR', 'ROI_Score')
            top_n (int): Number of top campaigns to return

        Returns:
            pandas.DataFrame: Top performing campaigns
        """
        try:
            campaign_performance = self.calculate_campaign_performance(df)

            if metric == 'CTR':
                return campaign_performance.nlargest(top_n, 'CTR')
            elif metric == 'ROI_Score':
                return campaign_performance.nlargest(top_n, 'ROI_Score')
            else:  # Default to Conversions
                return campaign_performance.nlargest(top_n, 'Conversions')

        except Exception as e:
            raise Exception(f"Error getting top performing campaigns: {str(e)}")

    def analyze_cost_efficiency(self, df):
        """
        Analyze cost efficiency across campaigns and time periods

        Args:
            df (pandas.DataFrame): Input dataframe

        Returns:
            dict: Cost efficiency analysis
        """
        try:
            analysis = {}

            # Campaign type efficiency
            type_efficiency = df.groupby('Campaign Type').agg({
                'Cost': 'sum',
                'Conversions': 'sum',
                'Clicks': 'sum'
            })

            type_efficiency['Cost_Per_Conversion'] = np.where(
                type_efficiency['Conversions'] > 0,
                type_efficiency['Cost'] / type_efficiency['Conversions'],
                float('inf')
            )

            type_efficiency['CPC'] = np.where(
                type_efficiency['Clicks'] > 0,
                type_efficiency['Cost'] / type_efficiency['Clicks'],
                0
            )

            analysis['campaign_type_efficiency'] = type_efficiency.to_dict('index')

            # Monthly cost trends
            monthly_costs = df.groupby(df['Date'].dt.to_period('M')).agg({
                'Cost': 'sum',
                'Conversions': 'sum'
            })

            monthly_costs['Cost_Per_Conversion'] = np.where(
                monthly_costs['Conversions'] > 0,
                monthly_costs['Cost'] / monthly_costs['Conversions'],
                float('inf')
            )

            analysis['monthly_trends'] = monthly_costs.to_dict('index')

            # Best and worst performing periods
            daily_performance = self.aggregate_by_period(df, 'D')
            analysis['best_day'] = daily_performance.loc[daily_performance['Total_Conversions'].idxmax()].to_dict()
            analysis['worst_day'] = daily_performance.loc[daily_performance['Total_Conversions'].idxmin()].to_dict()

            return analysis

        except Exception as e:
            raise Exception(f"Error analyzing cost efficiency: {str(e)}")

    def format_quality_report_text(self, quality_report):
        """
        Format quality report as readable text

        Args:
            quality_report (dict): Quality report data

        Returns:
            str: Formatted report text
        """
        try:
            report_lines = []
            report_lines.append("=== GOOGLE ADS DATA QUALITY REPORT ===\n")

            # Basic stats
            report_lines.append(f"Total Records: {quality_report['total_records']:,}")
            report_lines.append(f"Date Range: {quality_report['date_range'][0].strftime('%Y-%m-%d')} to {quality_report['date_range'][1].strftime('%Y-%m-%d')}")
            report_lines.append(f"Total Cost: ${quality_report['total_cost']:,.2f}")
            report_lines.append(f"Total Clicks: {quality_report['total_clicks']:,}")
            report_lines.append(f"Total Impressions: {quality_report['total_impressions']:,}")
            report_lines.append(f"Total Conversions: {quality_report['total_conversions']:,}")
            report_lines.append("")

            # Overall performance
            report_lines.append("=== OVERALL PERFORMANCE ===")
            report_lines.append(f"Overall CTR: {quality_report['overall_ctr']:.2f}%")
            report_lines.append(f"Overall CPC: ${quality_report['overall_cpc']:.2f}")
            report_lines.append(f"Overall Conversion Rate: {quality_report['overall_conversion_rate']:.2f}%")
            report_lines.append(f"Overall Cost per Conversion: ${quality_report['overall_cost_per_conversion']:.2f}")
            report_lines.append("")

            # Campaign info
            report_lines.append("=== CAMPAIGN INFORMATION ===")
            report_lines.append(f"Unique Campaigns: {quality_report['unique_campaigns']}")
            report_lines.append(f"Active Campaigns: {quality_report['active_campaigns']}")
            report_lines.append("Campaign Types:")
            for campaign_type, count in quality_report['campaign_types'].items():
                report_lines.append(f"  {campaign_type}: {count}")
            report_lines.append("")

            # Data completeness
            report_lines.append("=== DATA COMPLETENESS ===")
            for col, stats in quality_report['data_completeness'].items():
                report_lines.append(f"{col}: {stats['missing_percentage']:.1f}% missing ({stats['missing_count']:,} records)")

            return "\n".join(report_lines)

        except Exception as e:
            return f"Error formatting quality report: {str(e)}"
