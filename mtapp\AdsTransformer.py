#!/usr/bin/env python3
"""
Meta Ads Data Transformer - Complete Professional Edition
Combines the sophisticated original interface with Google Ads support
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import traceback

# Try to import ttkbootstrap, fallback to standard ttk
try:
    import ttkbootstrap as ttk
    from ttkbootstrap.constants import PRIMARY, SUCCESS, WARNING, DANGER, SECONDARY
    BOOTSTRAP_AVAILABLE = True
except ImportError:
    from tkinter import ttk
    BOOTSTRAP_AVAILABLE = False

class MetaAdsTransformerComplete:
    def __init__(self, root):
        self.root = root
        self.root.title("Meta Ads Data Transformer - Complete Professional Edition")
        self.root.geometry("1500x950")
        self.root.configure(bg='#f8f9fa')
        
        # Data storage
        self.raw_data = None
        self.gads_raw_data = None
        self.monthly_summary = None
        self.daily_aggregated = None
        self.gads_monthly_summary = None
        self.gads_daily_aggregated = None
        self.ad_insights = None
        self.data_quality_report = {}
        self.gads_quality_report = {}
        self.current_data_source = "meta"  # "meta" or "google"
        
        # Processing options
        self.advanced_data_sourcing = tk.BooleanVar(value=True)
        self.daily_aggregation = tk.BooleanVar(value=True)
        self.monthly_summary_var = tk.BooleanVar(value=True)
        self.airtable_export_format = tk.BooleanVar(value=False)
        self.summary_row_validation = tk.BooleanVar(value=True)

        # Date filtering options
        self.date_filter_active = False
        self.filter_start_date = None
        self.filter_end_date = None
        self.selected_month = tk.StringVar(value="All Months")
        self.selected_period = tk.StringVar(value="All Data")

        # Dashboard validation data
        self.dashboard_data = None
        self.dashboard_file_loaded = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # Enhanced main container with professional styling
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Configure enhanced main layout - left panel + right content
        main_container.columnconfigure(1, weight=1)
        main_container.rowconfigure(0, weight=1)

        # Enhanced left control panel
        self.setup_left_panel(main_container)

        # Enhanced right content area
        self.setup_content_area(main_container)

        # Enhanced status bar
        self.setup_status_bar()

    def setup_left_panel(self, parent):
        # Enhanced left panel frame with subtle background differentiation
        left_panel = ttk.Frame(parent, style='Panel.TFrame')
        left_panel.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))
        left_panel.configure(width=320)
        
        # Enhanced Data Source Selection with Professional Brand Icons
        source_frame = ttk.LabelFrame(left_panel, text="📊 Data Source", padding="20")
        source_frame.pack(fill=tk.X, pady=(0, 15))

        self.data_source_var = tk.StringVar(value="meta")

        # Enhanced Meta Ads option with professional brand styling
        meta_frame = ttk.Frame(source_frame)
        meta_frame.pack(fill=tk.X, pady=(0, 12))

        # Enhanced Meta icon with better styling
        meta_icon = ttk.Label(meta_frame, text="🔵", foreground="#1877f2", font=("Segoe UI", 18, "bold"))
        meta_icon.pack(side=tk.LEFT, padx=(0, 12))

        meta_radio = ttk.Radiobutton(meta_frame, text="Meta Ads (Facebook)",
                                   variable=self.data_source_var, value="meta",
                                   command=self.switch_data_source)
        meta_radio.pack(side=tk.LEFT)

        # Enhanced Google Ads option with professional brand styling
        google_frame = ttk.Frame(source_frame)
        google_frame.pack(fill=tk.X)

        # Enhanced Google icon with better styling
        google_icon = ttk.Label(google_frame, text="🔴", foreground="#ea4335", font=("Segoe UI", 18, "bold"))
        google_icon.pack(side=tk.LEFT, padx=(0, 12))

        google_radio = ttk.Radiobutton(google_frame, text="Google Ads",
                                     variable=self.data_source_var, value="google",
                                     command=self.switch_data_source)
        google_radio.pack(side=tk.LEFT)
        
        # File Operations Section with Enhanced Styling
        file_ops_frame = ttk.LabelFrame(left_panel, text="📁 File Operations", padding="15")
        file_ops_frame.pack(fill=tk.X, pady=(0, 10))

        # Enhanced Load Button with Professional Styling
        if BOOTSTRAP_AVAILABLE:
            self.load_btn = ttk.Button(file_ops_frame, text="📂 Load Meta Ads CSV",
                                      command=self.load_csv, width=28,
                                      bootstyle="outline-primary")
        else:
            self.load_btn = ttk.Button(file_ops_frame, text="📂 Load Meta Ads CSV",
                                      command=self.load_csv, width=28)
        self.load_btn.pack(pady=(0, 8))

        # Enhanced File Status Label
        self.file_status_label = ttk.Label(file_ops_frame, text="No file loaded",
                                          foreground='#6c757d', font=('Segoe UI', 8))
        self.file_status_label.pack(pady=(0, 8))

        # Enhanced Process & Validate Data Button - PRIMARY ACTION
        if BOOTSTRAP_AVAILABLE:
            self.validate_btn = ttk.Button(file_ops_frame, text="🔍 Process & Validate Data",
                                          command=self.process_and_validate, width=28,
                                          bootstyle="primary", state=tk.DISABLED)
        else:
            self.validate_btn = ttk.Button(file_ops_frame, text="🔍 Process & Validate Data",
                                          command=self.process_and_validate, width=28,
                                          state=tk.DISABLED)
        self.validate_btn.pack(pady=(0, 5))

        # Ensure button text is always visible - fallback mechanism
        def ensure_button_text_visible():
            """Ensure the button text remains visible after any state changes"""
            try:
                current_text = self.validate_btn.cget('text')
                if not current_text or current_text.strip() == '':
                    self.validate_btn.config(text="🔍 Process & Validate Data")
            except:
                pass

        # Bind to ensure text visibility
        self.validate_btn.bind('<Button-1>', lambda e: self.root.after(100, ensure_button_text_visible))
        self.validate_btn.bind('<ButtonRelease-1>', lambda e: self.root.after(100, ensure_button_text_visible))

        # Enhanced Help Text
        ttk.Label(file_ops_frame, text="Click to process loaded data",
                 font=('Segoe UI', 8), foreground='#6c757d').pack(pady=(0, 10))

        # Enhanced Separator
        separator = ttk.Separator(file_ops_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 10))

        # Enhanced Dashboard Button
        if BOOTSTRAP_AVAILABLE:
            self.dashboard_btn = ttk.Button(file_ops_frame, text="📊 Load Dashboard Data (Optional)",
                                           command=self.load_dashboard_data, width=28,
                                           bootstyle="outline-secondary")
        else:
            self.dashboard_btn = ttk.Button(file_ops_frame, text="📊 Load Dashboard Data (Optional)",
                                           command=self.load_dashboard_data, width=28)
        self.dashboard_btn.pack(pady=(0, 5))

        # Enhanced Dashboard Status Label
        self.dashboard_status_label = ttk.Label(file_ops_frame, text="No dashboard data loaded",
                                              foreground='#6c757d', font=('Segoe UI', 8))
        self.dashboard_status_label.pack(pady=(0, 5))
        
        # Enhanced Processing Options Section
        proc_opts_frame = ttk.LabelFrame(left_panel, text="⚙️ Processing Options", padding="15")
        proc_opts_frame.pack(fill=tk.X, pady=(0, 10))

        # Enhanced checkbuttons with better spacing and styling
        ttk.Checkbutton(proc_opts_frame, text="🔧 Advanced Data Sourcing",
                       variable=self.advanced_data_sourcing).pack(anchor=tk.W, pady=3)
        ttk.Checkbutton(proc_opts_frame, text="📈 Daily Aggregation",
                       variable=self.daily_aggregation).pack(anchor=tk.W, pady=3)
        ttk.Checkbutton(proc_opts_frame, text="📅 Monthly Summary",
                       variable=self.monthly_summary_var).pack(anchor=tk.W, pady=3)
        ttk.Checkbutton(proc_opts_frame, text="📤 Airtable Export Format",
                       variable=self.airtable_export_format).pack(anchor=tk.W, pady=3)
        ttk.Checkbutton(proc_opts_frame, text="✅ Summary Row Validation",
                       variable=self.summary_row_validation).pack(anchor=tk.W, pady=3)

        # Enhanced Date Filtering Section
        date_filter_frame = ttk.LabelFrame(left_panel, text="📅 Date Filtering", padding="15")
        date_filter_frame.pack(fill=tk.X, pady=(0, 10))

        # Enhanced Current filter status
        self.filter_status_label = ttk.Label(date_filter_frame, text="Filter: All Data",
                                           font=('Segoe UI', 9, 'bold'), foreground='#5294e2')
        self.filter_status_label.pack(pady=(0, 8))

        # Enhanced Month selection dropdown
        ttk.Label(date_filter_frame, text="Month Selection:",
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W)
        self.month_combo = ttk.Combobox(date_filter_frame, textvariable=self.selected_month,
                                       width=25, state="readonly", font=('Segoe UI', 9))
        self.month_combo.pack(pady=(2, 8), fill=tk.X)
        self.month_combo.bind('<<ComboboxSelected>>', self.on_month_selected)

        # Enhanced Predefined period buttons
        ttk.Label(date_filter_frame, text="Quick Periods:",
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W, pady=(0, 5))

        period_buttons_frame = ttk.Frame(date_filter_frame)
        period_buttons_frame.pack(fill=tk.X, pady=(0, 8))

        periods = [
            ("📅 14 days", 14),
            ("📅 30 days", 30),
            ("📅 45 days", 45),
            ("📅 60 days", 60),
            ("📅 90 days", 90)
        ]

        for i, (period_text, days) in enumerate(periods):
            if BOOTSTRAP_AVAILABLE:
                btn = ttk.Button(period_buttons_frame, text=period_text,
                               command=lambda d=days: self.apply_period_filter(d),
                               width=12, bootstyle="outline-info")
            else:
                btn = ttk.Button(period_buttons_frame, text=period_text,
                               command=lambda d=days: self.apply_period_filter(d),
                               width=12)
            row = i // 2
            col = i % 2
            btn.grid(row=row, column=col, padx=2, pady=2, sticky="ew")

        period_buttons_frame.columnconfigure(0, weight=1)
        period_buttons_frame.columnconfigure(1, weight=1)

        # Enhanced Custom date range
        ttk.Label(date_filter_frame, text="Custom Range:",
                 font=('Segoe UI', 9, 'bold')).pack(anchor=tk.W, pady=(5, 5))

        custom_range_frame = ttk.Frame(date_filter_frame)
        custom_range_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(custom_range_frame, text="From:", font=('Segoe UI', 8)).grid(row=0, column=0, sticky="w")
        self.start_date_entry = ttk.Entry(custom_range_frame, width=12, font=('Segoe UI', 9))
        self.start_date_entry.grid(row=0, column=1, padx=(2, 5))

        ttk.Label(custom_range_frame, text="To:", font=('Segoe UI', 8)).grid(row=0, column=2, sticky="w")
        self.end_date_entry = ttk.Entry(custom_range_frame, width=12, font=('Segoe UI', 9))
        self.end_date_entry.grid(row=0, column=3, padx=2)

        # Enhanced Custom range buttons
        custom_buttons_frame = ttk.Frame(date_filter_frame)
        custom_buttons_frame.pack(fill=tk.X, pady=2)

        if BOOTSTRAP_AVAILABLE:
            ttk.Button(custom_buttons_frame, text="✅ Apply Custom Range",
                      command=self.apply_custom_range, width=18,
                      bootstyle="success").pack(side=tk.LEFT, padx=(0, 3))
            ttk.Button(custom_buttons_frame, text="🗑️ Clear Filters",
                      command=self.clear_date_filters, width=12,
                      bootstyle="warning").pack(side=tk.LEFT)
        else:
            ttk.Button(custom_buttons_frame, text="✅ Apply Custom Range",
                      command=self.apply_custom_range, width=18).pack(side=tk.LEFT, padx=(0, 3))
            ttk.Button(custom_buttons_frame, text="🗑️ Clear Filters",
                      command=self.clear_date_filters, width=12).pack(side=tk.LEFT)

        # Enhanced Export Options Section
        export_frame = ttk.LabelFrame(left_panel, text="💾 Export Options", padding="15")
        export_frame.pack(fill=tk.X, pady=(0, 10))

        self.export_buttons = []
        export_configs = [
            ("📤 Export for Airtable", self.export_airtable, "success"),
            ("📊 Export Ad Performance", self.export_ad_performance, "outline-secondary"),
            ("🎯 Export Ad Insights", self.export_ad_insights, "outline-secondary"),
            ("📈 Export Daily Data", self.export_daily, "outline-secondary"),
            ("📅 Export Monthly Summary", self.export_monthly, "outline-secondary"),
            ("📋 Export Validation Report", self.export_validation_report, "warning")
        ]

        for btn_text, btn_command, btn_style in export_configs:
            if BOOTSTRAP_AVAILABLE:
                btn = ttk.Button(export_frame, text=btn_text, command=btn_command,
                               width=28, state=tk.DISABLED, bootstyle=btn_style)
            else:
                btn = ttk.Button(export_frame, text=btn_text, command=btn_command,
                               width=28, state=tk.DISABLED)
            btn.pack(pady=3)
            self.export_buttons.append(btn)
            
    def switch_data_source(self):
        """Switch between Meta Ads and Google Ads"""
        self.current_data_source = self.data_source_var.get()
        
        if self.current_data_source == "meta":
            self.load_btn.config(text="Load Meta Ads CSV")
            self.file_status_label.config(text="No Meta Ads file loaded")
        else:
            self.load_btn.config(text="Load Google Ads CSV")
            self.file_status_label.config(text="No Google Ads file loaded")
            
        # Update displays based on current data source
        self.update_displays_for_source()
        
    def setup_content_area(self, parent):
        # Enhanced right content area with professional styling
        content_frame = ttk.Frame(parent, style='Card.TFrame')
        content_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        content_frame.columnconfigure(0, weight=1)
        content_frame.rowconfigure(0, weight=1)

        # Enhanced main notebook for tabs with professional styling
        self.main_notebook = ttk.Notebook(content_frame)
        self.main_notebook.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Setup enhanced main tabs
        self.setup_validation_tab()
        self.setup_data_preview_tab()

    def setup_validation_tab(self):
        # Enhanced Validation Metrics & Methodology Tab
        validation_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(validation_frame, text="📊 Validation Metrics & Methodology")

        # Enhanced top section - Validation Methodology
        methodology_frame = ttk.LabelFrame(validation_frame, text="🔬 VALIDATION METHODOLOGY",
                                         padding="20")
        methodology_frame.pack(fill=tk.X, padx=15, pady=15)

        methodology_text = """
DATA COMPARISON APPROACHES:
• Meta Ads: Official Summary row from CSV export vs Our Calculation Sum
• Google Ads: Campaign totals vs Daily aggregation validation
• Validation: Compare totals within tolerance for accuracy verification

VALIDATION METRICS:
• Spend/Cost: Validates financial accuracy across platforms
• Results/Conversions: Validates conversion tracking consistency
• Reach/Impressions: Validates audience measurement accuracy
• Platform-specific: CTR, CPC for Google Ads; Frequency for Meta Ads
        """

        methodology_label = ttk.Label(methodology_frame, text=methodology_text,
                                    font=('Consolas', 10), justify=tk.LEFT)
        methodology_label.pack(anchor=tk.W)

        # Enhanced bottom section - Quality Report
        self.quality_frame = ttk.LabelFrame(validation_frame, text="📈 Data Quality Report",
                                          padding="20")
        self.quality_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # Enhanced quality report text widget with professional styling
        text_frame = ttk.Frame(self.quality_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)

        self.quality_text = tk.Text(text_frame, height=15, width=80,
                                   font=('Consolas', 10), state=tk.DISABLED,
                                   bg='#ffffff', relief=tk.FLAT, borderwidth=1,
                                   highlightthickness=1, highlightcolor='#5294e2')
        quality_scroll = ttk.Scrollbar(text_frame, orient=tk.VERTICAL,
                                      command=self.quality_text.yview)
        self.quality_text.configure(yscrollcommand=quality_scroll.set)

        self.quality_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        quality_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
    def setup_data_preview_tab(self):
        # Data Preview & Analysis Tab
        preview_frame = ttk.Frame(self.main_notebook)
        self.main_notebook.add(preview_frame, text="📋 Data Preview & Analysis")
        
        # Sub-notebook for different data views
        self.data_notebook = ttk.Notebook(preview_frame)
        self.data_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Setup data tabs (will be populated based on data source)
        self.setup_all_data_tabs()
        
    def setup_status_bar(self):
        # Enhanced status bar at bottom with professional styling
        self.status_var = tk.StringVar()
        self.status_var.set("🚀 Ready - Select data source and load CSV file to begin")

        status_frame = ttk.Frame(self.root, style='Card.TFrame')
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=(0, 15))

        status_bar = ttk.Label(status_frame, textvariable=self.status_var,
                              relief=tk.FLAT, anchor=tk.W, padding="10",
                              font=('Segoe UI', 9), foreground='#5294e2')
        status_bar.pack(fill=tk.X)

    def setup_all_data_tabs(self):
        """Setup all data tabs for both Meta Ads and Google Ads"""
        # Clear existing tabs
        for tab in self.data_notebook.tabs():
            self.data_notebook.forget(tab)

        # Setup tabs based on current data source
        if self.current_data_source == "meta":
            self.setup_meta_ads_tabs()
        else:
            self.setup_google_ads_tabs()

    def setup_meta_ads_tabs(self):
        """Setup Meta Ads specific tabs with enhanced styling"""
        # Enhanced Ad Insights Tab
        insights_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(insights_frame, text="🎯 Ad Insights")

        # Create container for enhanced table styling
        insights_container = ttk.Frame(insights_frame, style='Card.TFrame')
        insights_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Ad Name', 'Campaign Name', 'Spend', 'Results', 'Reach',
                  'Impressions', 'Frequency', 'Cost Per Result', 'Status',
                  'Start Date', 'End Date', 'Days Active', 'Category')

        self.insights_tree = ttk.Treeview(insights_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration with better widths and alignment
        column_configs = {
            'Ad Name': (150, tk.W),
            'Campaign Name': (150, tk.W),
            'Spend': (100, tk.E),
            'Results': (80, tk.E),
            'Reach': (100, tk.E),
            'Impressions': (100, tk.E),
            'Frequency': (80, tk.E),
            'Cost Per Result': (120, tk.E),
            'Status': (100, tk.CENTER),
            'Start Date': (100, tk.CENTER),
            'End Date': (100, tk.CENTER),
            'Days Active': (90, tk.CENTER),
            'Category': (120, tk.CENTER)
        }

        for col in columns:
            width, anchor = column_configs.get(col, (100, tk.CENTER))
            self.insights_tree.heading(col, text=col)
            self.insights_tree.column(col, width=width, anchor=anchor)

        # Enhanced scrollbars
        insights_scroll_y = ttk.Scrollbar(insights_container, orient=tk.VERTICAL,
                                        command=self.insights_tree.yview)
        insights_scroll_x = ttk.Scrollbar(insights_container, orient=tk.HORIZONTAL,
                                        command=self.insights_tree.xview)
        self.insights_tree.configure(yscrollcommand=insights_scroll_y.set,
                                   xscrollcommand=insights_scroll_x.set)

        self.insights_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        insights_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        insights_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors for better readability
        self.insights_tree.tag_configure('oddrow', background='#f8f9fa')
        self.insights_tree.tag_configure('evenrow', background='#ffffff')

        # Enhanced Ad Performance Tab
        performance_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(performance_frame, text="📊 Ad Performance")

        # Create container for enhanced table styling
        performance_container = ttk.Frame(performance_frame, style='Card.TFrame')
        performance_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Ad Name', 'Campaign', 'Spend', 'Results', 'Reach', 'Impressions',
                  'Cost Per Result', 'Frequency', 'Performance Score', 'Efficiency Rating')

        self.performance_tree = ttk.Treeview(performance_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration
        performance_column_configs = {
            'Ad Name': (150, tk.W),
            'Campaign': (150, tk.W),
            'Spend': (100, tk.E),
            'Results': (80, tk.E),
            'Reach': (100, tk.E),
            'Impressions': (100, tk.E),
            'Cost Per Result': (120, tk.E),
            'Frequency': (80, tk.E),
            'Performance Score': (130, tk.CENTER),
            'Efficiency Rating': (130, tk.CENTER)
        }

        for col in columns:
            width, anchor = performance_column_configs.get(col, (110, tk.CENTER))
            self.performance_tree.heading(col, text=col)
            self.performance_tree.column(col, width=width, anchor=anchor)

        performance_scroll_y = ttk.Scrollbar(performance_container, orient=tk.VERTICAL,
                                           command=self.performance_tree.yview)
        performance_scroll_x = ttk.Scrollbar(performance_container, orient=tk.HORIZONTAL,
                                           command=self.performance_tree.xview)
        self.performance_tree.configure(yscrollcommand=performance_scroll_y.set,
                                      xscrollcommand=performance_scroll_x.set)

        self.performance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        performance_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        performance_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors
        self.performance_tree.tag_configure('oddrow', background='#f8f9fa')
        self.performance_tree.tag_configure('evenrow', background='#ffffff')

        # Enhanced Airtable Export Preview Tab
        airtable_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(airtable_frame, text="📤 Airtable Export Preview")

        # Create container for enhanced table styling
        airtable_container = ttk.Frame(airtable_frame, style='Card.TFrame')
        airtable_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Date', 'Ad Name', 'Campaign', 'Spend', 'Results', 'Reach', 'Impressions')

        self.airtable_tree = ttk.Treeview(airtable_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration
        airtable_column_configs = {
            'Date': (100, tk.CENTER),
            'Ad Name': (150, tk.W),
            'Campaign': (150, tk.W),
            'Spend': (100, tk.E),
            'Results': (80, tk.E),
            'Reach': (100, tk.E),
            'Impressions': (100, tk.E)
        }

        for col in columns:
            width, anchor = airtable_column_configs.get(col, (120, tk.CENTER))
            self.airtable_tree.heading(col, text=col)
            self.airtable_tree.column(col, width=width, anchor=anchor)

        airtable_scroll_y = ttk.Scrollbar(airtable_container, orient=tk.VERTICAL,
                                        command=self.airtable_tree.yview)
        airtable_scroll_x = ttk.Scrollbar(airtable_container, orient=tk.HORIZONTAL,
                                        command=self.airtable_tree.xview)
        self.airtable_tree.configure(yscrollcommand=airtable_scroll_y.set,
                                   xscrollcommand=airtable_scroll_x.set)

        self.airtable_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        airtable_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        airtable_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors
        self.airtable_tree.tag_configure('oddrow', background='#f8f9fa')
        self.airtable_tree.tag_configure('evenrow', background='#ffffff')

        # Enhanced Summary Raw Details Tab (Meta vs Our calculations)
        summary_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(summary_frame, text="📊 Summary Raw Details")

        # Create container for enhanced table styling
        summary_container = ttk.Frame(summary_frame, style='Card.TFrame')
        summary_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Metric', 'Meta Summary', 'Our Calculation', 'Difference', 'Variance %', 'Status')

        self.summary_tree = ttk.Treeview(summary_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration
        summary_column_configs = {
            'Metric': (150, tk.W),
            'Meta Summary': (120, tk.E),
            'Our Calculation': (120, tk.E),
            'Difference': (100, tk.E),
            'Variance %': (100, tk.E),
            'Status': (100, tk.CENTER)
        }

        for col in columns:
            width, anchor = summary_column_configs.get(col, (150, tk.CENTER))
            self.summary_tree.heading(col, text=col)
            self.summary_tree.column(col, width=width, anchor=anchor)

        summary_scroll_y = ttk.Scrollbar(summary_container, orient=tk.VERTICAL,
                                       command=self.summary_tree.yview)
        summary_scroll_x = ttk.Scrollbar(summary_container, orient=tk.HORIZONTAL,
                                       command=self.summary_tree.xview)
        self.summary_tree.configure(yscrollcommand=summary_scroll_y.set,
                                  xscrollcommand=summary_scroll_x.set)

        self.summary_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        summary_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        summary_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors
        self.summary_tree.tag_configure('oddrow', background='#f8f9fa')
        self.summary_tree.tag_configure('evenrow', background='#ffffff')

        # Enhanced Monthly Summary Tab
        monthly_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(monthly_frame, text="📅 Monthly Summary")

        # Create container for enhanced table styling
        monthly_container = ttk.Frame(monthly_frame, style='Card.TFrame')
        monthly_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Month', 'Total Spend', 'Total Results', 'Total Reach',
                  'Total Impressions', 'Avg Frequency', 'Cost Per Result', 'Ad Count')
        self.monthly_tree = ttk.Treeview(monthly_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration
        monthly_column_configs = {
            'Month': (100, tk.CENTER),
            'Total Spend': (120, tk.E),
            'Total Results': (100, tk.E),
            'Total Reach': (120, tk.E),
            'Total Impressions': (130, tk.E),
            'Avg Frequency': (120, tk.E),
            'Cost Per Result': (120, tk.E),
            'Ad Count': (80, tk.E)
        }

        for col in columns:
            width, anchor = monthly_column_configs.get(col, (120, tk.CENTER))
            self.monthly_tree.heading(col, text=col)
            self.monthly_tree.column(col, width=width, anchor=anchor)

        monthly_scroll_y = ttk.Scrollbar(monthly_container, orient=tk.VERTICAL,
                                       command=self.monthly_tree.yview)
        monthly_scroll_x = ttk.Scrollbar(monthly_container, orient=tk.HORIZONTAL,
                                       command=self.monthly_tree.xview)
        self.monthly_tree.configure(yscrollcommand=monthly_scroll_y.set,
                                  xscrollcommand=monthly_scroll_x.set)

        self.monthly_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        monthly_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        monthly_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors
        self.monthly_tree.tag_configure('oddrow', background='#f8f9fa')
        self.monthly_tree.tag_configure('evenrow', background='#ffffff')

        # Enhanced Daily Aggregated Tab
        daily_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(daily_frame, text="📈 Daily Aggregated")

        # Create container for enhanced table styling
        daily_container = ttk.Frame(daily_frame, style='Card.TFrame')
        daily_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        columns = ('Date', 'Total Spend', 'Total Results', 'Total Reach',
                  'Total Impressions', 'Frequency', 'Cost Per Result', 'Active Ads')
        self.daily_tree = ttk.Treeview(daily_container, columns=columns, show='headings', height=15)

        # Enhanced column configuration
        daily_column_configs = {
            'Date': (100, tk.CENTER),
            'Total Spend': (120, tk.E),
            'Total Results': (100, tk.E),
            'Total Reach': (120, tk.E),
            'Total Impressions': (130, tk.E),
            'Frequency': (100, tk.E),
            'Cost Per Result': (120, tk.E),
            'Active Ads': (90, tk.E)
        }

        for col in columns:
            width, anchor = daily_column_configs.get(col, (110, tk.CENTER))
            self.daily_tree.heading(col, text=col)
            self.daily_tree.column(col, width=width, anchor=anchor)

        daily_scroll_y = ttk.Scrollbar(daily_container, orient=tk.VERTICAL,
                                     command=self.daily_tree.yview)
        daily_scroll_x = ttk.Scrollbar(daily_container, orient=tk.HORIZONTAL,
                                     command=self.daily_tree.xview)
        self.daily_tree.configure(yscrollcommand=daily_scroll_y.set,
                                xscrollcommand=daily_scroll_x.set)

        self.daily_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        daily_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        daily_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Configure alternating row colors
        self.daily_tree.tag_configure('oddrow', background='#f8f9fa')
        self.daily_tree.tag_configure('evenrow', background='#ffffff')

        # Cleaned Data Tab (All 21 Meta Ads columns)
        cleaned_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(cleaned_frame, text="🧹 Cleaned Data")

        columns = ('Reporting starts', 'Reporting ends', 'Ad name', 'Ad delivery', 'Ad Set Name',
                  'Bid', 'Bid type', 'Ad set budget', 'Ad set budget type', 'Last significant edit',
                  'Attribution setting', 'Results', 'Result indicator', 'Reach', 'Impressions',
                  'Cost per results', 'Quality ranking', 'Engagement rate ranking',
                  'Conversion rate ranking', 'Amount spent (USD)', 'Ends')

        self.cleaned_tree = ttk.Treeview(cleaned_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.cleaned_tree.heading(col, text=col)
            self.cleaned_tree.column(col, width=100, anchor=tk.CENTER)

        cleaned_scroll_y = ttk.Scrollbar(cleaned_frame, orient=tk.VERTICAL,
                                       command=self.cleaned_tree.yview)
        cleaned_scroll_x = ttk.Scrollbar(cleaned_frame, orient=tk.HORIZONTAL,
                                       command=self.cleaned_tree.xview)
        self.cleaned_tree.configure(yscrollcommand=cleaned_scroll_y.set,
                                  xscrollcommand=cleaned_scroll_x.set)

        self.cleaned_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cleaned_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        cleaned_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

    def setup_google_ads_tabs(self):
        """Setup Google Ads specific tabs"""
        # Campaign Performance Tab
        campaign_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(campaign_frame, text="🎯 Campaign Performance")

        # Create main container for tree and summary
        campaign_container = ttk.Frame(campaign_frame)
        campaign_container.pack(fill=tk.BOTH, expand=True)

        columns = ('Campaign', 'Cost', 'Clicks', 'Impressions', 'Conversions',
                  'CTR', 'CPC', 'Conv Rate', 'Cost/Conv', 'ROI Score')

        self.gads_campaign_tree = ttk.Treeview(campaign_container, columns=columns, show='headings', height=13)

        for col in columns:
            self.gads_campaign_tree.heading(col, text=col)
            self.gads_campaign_tree.column(col, width=100, anchor=tk.CENTER)

        gads_campaign_scroll_y = ttk.Scrollbar(campaign_container, orient=tk.VERTICAL,
                                             command=self.gads_campaign_tree.yview)
        gads_campaign_scroll_x = ttk.Scrollbar(campaign_container, orient=tk.HORIZONTAL,
                                             command=self.gads_campaign_tree.xview)
        self.gads_campaign_tree.configure(yscrollcommand=gads_campaign_scroll_y.set,
                                        xscrollcommand=gads_campaign_scroll_x.set)

        self.gads_campaign_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        gads_campaign_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        gads_campaign_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Campaign Performance Summary totals frame
        campaign_summary_frame = ttk.LabelFrame(campaign_frame, text="📊 Campaign Totals", padding="5")
        campaign_summary_frame.pack(fill=tk.X, pady=(5, 0))

        # Summary labels for campaign performance
        self.gads_campaign_summary_labels = {}
        campaign_summary_grid = ttk.Frame(campaign_summary_frame)
        campaign_summary_grid.pack(fill=tk.X)

        # Create summary labels in a grid layout
        campaign_summary_metrics = [
            ('Total Cost', 'campaign_total_cost'),
            ('Total Clicks', 'campaign_total_clicks'),
            ('Total Impressions', 'campaign_total_impressions'),
            ('Total Conversions', 'campaign_total_conversions')
        ]

        for i, (label_text, key) in enumerate(campaign_summary_metrics):
            row = i // 2
            col = i % 2

            ttk.Label(campaign_summary_grid, text=f"{label_text}:", font=('Arial', 9, 'bold')).grid(
                row=row, column=col*2, sticky='w', padx=(5, 2), pady=2)

            self.gads_campaign_summary_labels[key] = ttk.Label(campaign_summary_grid, text="$0.00",
                                                             font=('Arial', 9), foreground='blue')
            self.gads_campaign_summary_labels[key].grid(row=row, column=col*2+1, sticky='w', padx=(2, 15), pady=2)

        # Configure grid weights
        for i in range(4):
            campaign_summary_grid.columnconfigure(i, weight=1)

        # Monthly Summary Tab for Google Ads
        gads_monthly_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(gads_monthly_frame, text="📅 Monthly Summary")

        # Create main container for tree and summary
        monthly_container = ttk.Frame(gads_monthly_frame)
        monthly_container.pack(fill=tk.BOTH, expand=True)

        columns = ('Month', 'Total Cost', 'Total Clicks', 'Total Impressions',
                  'Total Conversions', 'Avg CTR', 'Avg CPC', 'Avg Conv Rate')
        self.gads_monthly_tree = ttk.Treeview(monthly_container, columns=columns, show='headings', height=13)

        for col in columns:
            self.gads_monthly_tree.heading(col, text=col)
            self.gads_monthly_tree.column(col, width=120, anchor=tk.CENTER)

        gads_monthly_scroll_y = ttk.Scrollbar(monthly_container, orient=tk.VERTICAL,
                                            command=self.gads_monthly_tree.yview)
        gads_monthly_scroll_x = ttk.Scrollbar(monthly_container, orient=tk.HORIZONTAL,
                                            command=self.gads_monthly_tree.xview)
        self.gads_monthly_tree.configure(yscrollcommand=gads_monthly_scroll_y.set,
                                       xscrollcommand=gads_monthly_scroll_x.set)

        self.gads_monthly_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        gads_monthly_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        gads_monthly_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Monthly Summary totals frame
        monthly_summary_frame = ttk.LabelFrame(gads_monthly_frame, text="📊 Period Totals", padding="5")
        monthly_summary_frame.pack(fill=tk.X, pady=(5, 0))

        # Summary labels for monthly data
        self.gads_monthly_summary_labels = {}
        monthly_summary_grid = ttk.Frame(monthly_summary_frame)
        monthly_summary_grid.pack(fill=tk.X)

        # Create summary labels in a grid layout
        monthly_summary_metrics = [
            ('Total Cost', 'monthly_total_cost'),
            ('Total Clicks', 'monthly_total_clicks'),
            ('Total Impressions', 'monthly_total_impressions'),
            ('Total Conversions', 'monthly_total_conversions')
        ]

        for i, (label_text, key) in enumerate(monthly_summary_metrics):
            row = i // 2
            col = i % 2

            ttk.Label(monthly_summary_grid, text=f"{label_text}:", font=('Arial', 9, 'bold')).grid(
                row=row, column=col*2, sticky='w', padx=(5, 2), pady=2)

            self.gads_monthly_summary_labels[key] = ttk.Label(monthly_summary_grid, text="$0.00",
                                                            font=('Arial', 9), foreground='blue')
            self.gads_monthly_summary_labels[key].grid(row=row, column=col*2+1, sticky='w', padx=(2, 15), pady=2)

        # Configure grid weights
        for i in range(4):
            monthly_summary_grid.columnconfigure(i, weight=1)

        # Dashboard Validation Matrix Tab
        validation_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(validation_frame, text="🔍 Dashboard Validation")

        # Create container for dynamic content
        self.validation_container = ttk.Frame(validation_frame)
        self.validation_container.pack(fill=tk.BOTH, expand=True)

        # Initialize with basic columns (will be updated dynamically)
        self.setup_validation_display()

        # Campaign Performance Deep Dive Tab
        deep_dive_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(deep_dive_frame, text="📊 Campaign Deep Dive")

        columns = ('Campaign', 'Performance Score', 'Efficiency Rating', 'Lifecycle Stage',
                  'Optimization Opportunity', 'Budget Utilization', 'Trend Direction', 'Quality Score')
        self.gads_deep_dive_tree = ttk.Treeview(deep_dive_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.gads_deep_dive_tree.heading(col, text=col)
            self.gads_deep_dive_tree.column(col, width=140, anchor=tk.CENTER)

        deep_dive_scroll_y = ttk.Scrollbar(deep_dive_frame, orient=tk.VERTICAL,
                                         command=self.gads_deep_dive_tree.yview)
        deep_dive_scroll_x = ttk.Scrollbar(deep_dive_frame, orient=tk.HORIZONTAL,
                                         command=self.gads_deep_dive_tree.xview)
        self.gads_deep_dive_tree.configure(yscrollcommand=deep_dive_scroll_y.set,
                                         xscrollcommand=deep_dive_scroll_x.set)

        self.gads_deep_dive_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        deep_dive_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        deep_dive_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Temporal Analysis Tab
        temporal_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(temporal_frame, text="📈 Temporal Analysis")

        columns = ('Period', 'Performance Velocity', 'Trend Direction', 'Anomaly Score',
                  'Pattern Type', 'Forecast Accuracy', 'Optimization Window', 'Risk Level')
        self.gads_temporal_tree = ttk.Treeview(temporal_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.gads_temporal_tree.heading(col, text=col)
            self.gads_temporal_tree.column(col, width=130, anchor=tk.CENTER)

        temporal_scroll_y = ttk.Scrollbar(temporal_frame, orient=tk.VERTICAL,
                                        command=self.gads_temporal_tree.yview)
        temporal_scroll_x = ttk.Scrollbar(temporal_frame, orient=tk.HORIZONTAL,
                                        command=self.gads_temporal_tree.xview)
        self.gads_temporal_tree.configure(yscrollcommand=temporal_scroll_y.set,
                                        xscrollcommand=temporal_scroll_x.set)

        self.gads_temporal_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        temporal_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        temporal_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Raw Google Ads Data Tab
        gads_raw_frame = ttk.Frame(self.data_notebook)
        self.data_notebook.add(gads_raw_frame, text="📄 Raw Data")

        # Create main container for tree and summary
        gads_raw_container = ttk.Frame(gads_raw_frame)
        gads_raw_container.pack(fill=tk.BOTH, expand=True)

        # Tree view for data
        columns = ('Date', 'Campaign', 'Cost', 'Clicks', 'Impressions', 'Conversions', 'CTR', 'CPC')
        self.gads_raw_tree = ttk.Treeview(gads_raw_container, columns=columns, show='headings', height=13)

        for col in columns:
            self.gads_raw_tree.heading(col, text=col)
            self.gads_raw_tree.column(col, width=120, anchor=tk.CENTER)

        gads_raw_scroll_y = ttk.Scrollbar(gads_raw_container, orient=tk.VERTICAL,
                                        command=self.gads_raw_tree.yview)
        gads_raw_scroll_x = ttk.Scrollbar(gads_raw_container, orient=tk.HORIZONTAL,
                                        command=self.gads_raw_tree.xview)
        self.gads_raw_tree.configure(yscrollcommand=gads_raw_scroll_y.set,
                                   xscrollcommand=gads_raw_scroll_x.set)

        self.gads_raw_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        gads_raw_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        gads_raw_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Summary totals frame
        gads_summary_frame = ttk.LabelFrame(gads_raw_frame, text="📊 Summary Totals", padding="5")
        gads_summary_frame.pack(fill=tk.X, pady=(5, 0))

        # Summary labels
        self.gads_summary_labels = {}
        summary_grid = ttk.Frame(gads_summary_frame)
        summary_grid.pack(fill=tk.X)

        # Create summary labels in a grid layout
        summary_metrics = [
            ('Total Cost', 'total_cost'),
            ('Total Clicks', 'total_clicks'),
            ('Total Impressions', 'total_impressions'),
            ('Total Conversions', 'total_conversions')
        ]

        for i, (label_text, key) in enumerate(summary_metrics):
            row = i // 2
            col = i % 2

            ttk.Label(summary_grid, text=f"{label_text}:", font=('Arial', 9, 'bold')).grid(
                row=row, column=col*2, sticky='w', padx=(5, 2), pady=2)

            self.gads_summary_labels[key] = ttk.Label(summary_grid, text="$0.00",
                                                    font=('Arial', 9), foreground='blue')
            self.gads_summary_labels[key].grid(row=row, column=col*2+1, sticky='w', padx=(2, 15), pady=2)

        # Configure grid weights
        for i in range(4):
            summary_grid.columnconfigure(i, weight=1)

    def load_csv(self):
        """Load CSV file based on selected data source"""
        try:
            if self.current_data_source == "meta":
                file_path = filedialog.askopenfilename(
                    title="Select Meta Ads CSV File",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )
            else:
                file_path = filedialog.askopenfilename(
                    title="Select Google Ads CSV File",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )

            if not file_path:
                return

            self.status_var.set("Loading and validating CSV file...")
            self.root.update()

            # Load data based on source
            if self.current_data_source == "meta":
                self.raw_data = self.load_and_validate_meta_csv(file_path)
                if self.raw_data is not None:
                    self.file_status_label.config(text=f"Meta Ads: {len(self.raw_data)} records loaded")
                    self.status_var.set(f"Meta Ads: {len(self.raw_data)} records with {len(self.raw_data.columns)} columns loaded")
                else:
                    self.file_status_label.config(text="Failed to load Meta Ads file")
                    self.status_var.set("Failed to load Meta Ads CSV file")
            else:
                self.gads_raw_data = self.load_and_validate_gads_csv(file_path)
                if self.gads_raw_data is not None:
                    self.file_status_label.config(text=f"Google Ads: {len(self.gads_raw_data)} records loaded")
                    self.status_var.set(f"Google Ads: {len(self.gads_raw_data)} records with {len(self.gads_raw_data.columns)} columns loaded")
                else:
                    self.file_status_label.config(text="Failed to load Google Ads file")
                    self.status_var.set("Failed to load Google Ads CSV file")

            # Enable validate button if data is loaded
            if (self.current_data_source == "meta" and self.raw_data is not None) or \
               (self.current_data_source == "google" and self.gads_raw_data is not None):
                self.validate_btn.config(state=tk.NORMAL)

        except Exception as e:
            self.handle_error("Error loading CSV", e)

    def load_and_validate_meta_csv(self, file_path):
        """Load and validate Meta Ads CSV with all 21 columns"""
        try:
            # Load CSV
            df = pd.read_csv(file_path)

            # Skip summary row if present
            if len(df) > 1 and self.summary_row_validation.get():
                df = df.iloc[1:].copy()

            # Clean column names
            df.columns = [c.strip() for c in df.columns]

            # Core required columns
            required_columns = ['Reporting ends', 'Amount spent (USD)', 'Results', 'Reach', 'Impressions']
            missing_required = [col for col in required_columns if col not in df.columns]

            if missing_required:
                messagebox.showerror("Missing Required Columns",
                                   f"Required Meta Ads columns missing: {', '.join(missing_required)}")
                return None

            # Convert data types with UTC specification to avoid FutureWarning
            df['Reporting ends'] = pd.to_datetime(df['Reporting ends'], errors='coerce', utc=True).dt.tz_localize(None)
            if 'Reporting starts' in df.columns:
                df['Reporting starts'] = pd.to_datetime(df['Reporting starts'], errors='coerce', utc=True).dt.tz_localize(None)
            if 'Last significant edit' in df.columns:
                df['Last significant edit'] = pd.to_datetime(df['Last significant edit'], errors='coerce', utc=True).dt.tz_localize(None)

            # Convert numeric columns
            numeric_columns = ['Amount spent (USD)', 'Results', 'Reach', 'Impressions', 'Bid',
                             'Ad set budget', 'Cost per results']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # Remove invalid data
            initial_count = len(df)
            df = df[df['Reporting ends'].notnull()].copy()

            if len(df) == 0:
                messagebox.showerror("Data Error", "No valid data found after cleaning")
                return None

            # Store comprehensive validation info
            self.data_quality_report = {
                'initial_rows': initial_count,
                'valid_rows': len(df),
                'date_range': (df['Reporting ends'].min(), df['Reporting ends'].max()),
                'total_spend': df['Amount spent (USD)'].sum(),
                'total_results': df['Results'].sum(),
                'total_reach': df['Reach'].sum(),
                'total_impressions': df['Impressions'].sum(),
                'unique_ads': df['Ad name'].nunique() if 'Ad name' in df.columns else 0,
                'unique_ad_sets': df['Ad Set Name'].nunique() if 'Ad Set Name' in df.columns else 0,
                'delivery_statuses': df['Ad delivery'].value_counts().to_dict() if 'Ad delivery' in df.columns else {},
                'bid_types': df['Bid type'].value_counts().to_dict() if 'Bid type' in df.columns else {}
            }

            return df

        except Exception as e:
            messagebox.showerror("File Error", f"Error reading Meta Ads CSV file: {str(e)}")
            return None

    def load_and_validate_gads_csv(self, file_path):
        """Load and validate Google Ads CSV"""
        try:
            # Load CSV
            df = pd.read_csv(file_path)

            # Clean column names
            df.columns = [c.strip() for c in df.columns]

            # Map column names to standard format
            column_mapping = {
                'Date': 'Day',
                'Campaign Name': 'Campaign',
                'Impressions': 'Impr.',
                'Conv. Rate': 'Conv_Rate',
                'Cost per Conv.': 'Cost_Per_Conv'
            }

            # Rename columns to match expected format
            df = df.rename(columns=column_mapping)

            # Required Google Ads columns (after mapping)
            required_columns = ['Day', 'Campaign', 'Cost', 'Clicks', 'Impr.', 'Conversions']
            missing_required = [col for col in required_columns if col not in df.columns]

            if missing_required:
                messagebox.showerror("Missing Required Columns",
                                   f"Required Google Ads columns missing: {', '.join(missing_required)}\n"
                                   f"Available columns: {', '.join(df.columns)}")
                return None

            # Convert data types
            df['Day'] = pd.to_datetime(df['Day'], errors='coerce')

            # Convert numeric columns with better error handling (using mapped column names)
            numeric_columns = ['Cost', 'Clicks', 'Impr.', 'Conversions', 'CTR', 'CPC', 'Conv_Rate', 'Cost_Per_Conv']
            for col in numeric_columns:
                if col in df.columns:
                    try:
                        # Handle percentage columns (using mapped column names)
                        if col in ['CTR', 'Conv_Rate'] and df[col].dtype == 'object':
                            # Remove % symbol and convert to decimal
                            df[col] = df[col].astype(str).str.replace('%', '').str.replace(',', '')
                            df[col] = pd.to_numeric(df[col], errors='coerce') / 100
                        else:
                            # Handle currency and comma-separated numbers
                            if df[col].dtype == 'object':
                                df[col] = df[col].astype(str).str.replace('$', '').str.replace(',', '')
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                    except Exception as e:
                        print(f"Warning: Error converting column {col}: {e}")
                        df[col] = pd.to_numeric(df[col], errors='coerce')

            # Remove invalid data
            initial_count = len(df)
            df = df[df['Day'].notnull()].copy()

            if len(df) == 0:
                messagebox.showerror("Data Error", "No valid Google Ads data found after cleaning")
                return None

            # Store Google Ads quality report
            self.gads_quality_report = {
                'initial_rows': initial_count,
                'valid_rows': len(df),
                'date_range': (df['Day'].min(), df['Day'].max()),
                'total_cost': df['Cost'].sum(),
                'total_clicks': df['Clicks'].sum(),
                'total_impressions': df['Impr.'].sum(),
                'total_conversions': df['Conversions'].sum(),
                'unique_campaigns': df['Campaign'].nunique(),
                'avg_ctr': df['CTR'].mean() if 'CTR' in df.columns else 0,
                'avg_cpc': df['CPC'].mean() if 'CPC' in df.columns else 0
            }

            return df

        except Exception as e:
            messagebox.showerror("File Error", f"Error reading Google Ads CSV file: {str(e)}")
            return None

    def populate_month_dropdown(self):
        """Populate month dropdown with available months from data"""
        try:
            months = ["All Months"]

            if self.current_data_source == "meta" and self.raw_data is not None:
                # Get unique months from Meta Ads data
                data_months = self.raw_data['Reporting ends'].dt.to_period('M').unique()
                months.extend([str(month) for month in sorted(data_months)])
            elif self.current_data_source == "google" and self.gads_raw_data is not None:
                # Get unique months from Google Ads data
                data_months = self.gads_raw_data['Day'].dt.to_period('M').unique()
                months.extend([str(month) for month in sorted(data_months)])

            self.month_combo['values'] = months
            self.month_combo.set("All Months")

        except Exception as e:
            print(f"Error populating month dropdown: {e}")

    def on_month_selected(self, event=None):
        """Handle month selection from dropdown"""
        selected_month = self.selected_month.get()

        if selected_month == "All Months":
            self.clear_date_filters()
        else:
            try:
                # Parse the selected month (format: "2024-01")
                year_month = pd.Period(selected_month)
                start_date = year_month.start_time.date()
                end_date = year_month.end_time.date()

                self.apply_date_filter(start_date, end_date, f"Month: {selected_month}")

            except Exception as e:
                messagebox.showerror("Date Error", f"Error applying month filter: {str(e)}")

    def apply_period_filter(self, days):
        """Apply predefined period filter based on latest date in data"""
        try:
            # Get the latest date from the current data source
            if self.current_data_source == "meta" and self.raw_data is not None:
                latest_date = self.raw_data['Reporting ends'].max().date()
            elif self.current_data_source == "google" and self.gads_raw_data is not None:
                latest_date = self.gads_raw_data['Day'].max().date()
            else:
                messagebox.showwarning("No Data", "Please load data first before applying filters")
                return

            # Calculate start date from the latest date in data
            start_date = latest_date - timedelta(days=days-1)  # -1 to include the end date
            end_date = latest_date

            filter_label = f"Last {days} days (from {latest_date})"
            self.apply_date_filter(start_date, end_date, filter_label)

        except Exception as e:
            messagebox.showerror("Date Error", f"Error applying period filter: {str(e)}")

    def apply_custom_range(self):
        """Apply custom date range filter"""
        try:
            start_str = self.start_date_entry.get().strip()
            end_str = self.end_date_entry.get().strip()

            if not start_str or not end_str:
                # Show available date range to help user
                available_range = self.get_available_date_range()
                if available_range:
                    messagebox.showwarning("Date Error",
                                         f"Please enter both start and end dates (YYYY-MM-DD format)\n\n"
                                         f"Available data range: {available_range[0]} to {available_range[1]}")
                else:
                    messagebox.showwarning("Date Error", "Please enter both start and end dates (YYYY-MM-DD format)")
                return

            start_date = datetime.strptime(start_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_str, "%Y-%m-%d").date()

            if start_date > end_date:
                messagebox.showerror("Date Error", "Start date must be before end date")
                return

            # Check if dates are within available range
            available_range = self.get_available_date_range()
            if available_range:
                if start_date < available_range[0] or end_date > available_range[1]:
                    messagebox.showwarning("Date Range Warning",
                                         f"Selected range extends beyond available data.\n\n"
                                         f"Available data: {available_range[0]} to {available_range[1]}\n"
                                         f"Selected range: {start_date} to {end_date}\n\n"
                                         f"Filter will be applied but may show limited results.")

            filter_label = f"Custom: {start_str} to {end_str}"
            self.apply_date_filter(start_date, end_date, filter_label)

        except ValueError:
            available_range = self.get_available_date_range()
            if available_range:
                messagebox.showerror("Date Error",
                                   f"Please enter dates in YYYY-MM-DD format (e.g., 2025-02-20)\n\n"
                                   f"Available data range: {available_range[0]} to {available_range[1]}")
            else:
                messagebox.showerror("Date Error", "Please enter dates in YYYY-MM-DD format (e.g., 2025-02-20)")
        except Exception as e:
            messagebox.showerror("Date Error", f"Error applying custom range: {str(e)}")

    def get_available_date_range(self):
        """Get the available date range from current data"""
        try:
            if self.current_data_source == "meta" and self.raw_data is not None:
                min_date = self.raw_data['Reporting ends'].min().date()
                max_date = self.raw_data['Reporting ends'].max().date()
                return (min_date, max_date)
            elif self.current_data_source == "google" and self.gads_raw_data is not None:
                min_date = self.gads_raw_data['Day'].min().date()
                max_date = self.gads_raw_data['Day'].max().date()
                return (min_date, max_date)
            else:
                return None
        except Exception as e:
            print(f"Error getting available date range: {e}")
            return None

    def apply_date_filter(self, start_date, end_date, filter_label):
        """Apply date filter and refresh all displays"""
        try:
            self.date_filter_active = True
            self.filter_start_date = start_date
            self.filter_end_date = end_date

            # Update filter status display
            self.filter_status_label.config(text=f"Filter: {filter_label}")

            # Reset month dropdown if not a month filter
            if not filter_label.startswith("Month:"):
                self.month_combo.set("All Months")

            # Refresh all displays with filtered data
            self.refresh_filtered_displays()

            self.status_var.set(f"Applied filter: {filter_label}")

        except Exception as e:
            self.handle_error("Error applying date filter", e)

    def clear_date_filters(self):
        """Clear all date filters and show all data"""
        try:
            self.date_filter_active = False
            self.filter_start_date = None
            self.filter_end_date = None

            # Reset UI elements
            self.filter_status_label.config(text="Filter: All Data")
            self.month_combo.set("All Months")
            self.start_date_entry.delete(0, tk.END)
            self.end_date_entry.delete(0, tk.END)

            # Refresh all displays with full data
            self.refresh_filtered_displays()

            self.status_var.set("Cleared all date filters - showing all data")

        except Exception as e:
            self.handle_error("Error clearing date filters", e)

    def load_dashboard_data(self):
        """Load dashboard data for validation comparison"""
        try:
            file_path = filedialog.askopenfilename(
                title="Select Dashboard Data File (CSV or JSON)",
                filetypes=[("CSV files", "*.csv"), ("JSON files", "*.json"), ("All files", "*.*")]
            )

            if not file_path:
                return

            self.status_var.set("Loading dashboard data...")
            self.root.update()

            # Load based on file extension
            if file_path.lower().endswith('.csv'):
                self.dashboard_data = pd.read_csv(file_path)
            elif file_path.lower().endswith('.json'):
                self.dashboard_data = pd.read_json(file_path)
            else:
                messagebox.showerror("File Error", "Please select a CSV or JSON file")
                return

            self.dashboard_file_loaded = True
            self.dashboard_status_label.config(text=f"Dashboard data: {len(self.dashboard_data)} records loaded")
            self.status_var.set(f"Dashboard data loaded: {len(self.dashboard_data)} records")

            # Refresh validation display with new columns
            self.setup_validation_display()
            if hasattr(self, 'gads_raw_data') and self.gads_raw_data is not None:
                self.update_gads_validation_display()

        except Exception as e:
            self.handle_error("Error loading dashboard data", e)
            self.dashboard_file_loaded = False
            self.dashboard_data = None

    def setup_validation_display(self):
        """Setup validation display with dynamic columns based on dashboard data availability"""
        try:
            # Clear existing validation display
            for widget in self.validation_container.winfo_children():
                widget.destroy()

            if self.dashboard_file_loaded and self.dashboard_data is not None:
                # Full validation columns when dashboard data is available
                columns = ('Metric', 'Web Dashboard', 'CSV Data', 'Difference', 'Variance %', 'Status', 'Data Quality')
                info_text = "Dashboard data loaded - showing full validation comparison"
            else:
                # Basic columns when no dashboard data
                columns = ('Metric', 'CSV Data', 'Data Quality')
                info_text = "No dashboard data loaded - showing CSV data only. Load dashboard data for full validation."

            # Info label
            info_label = ttk.Label(self.validation_container, text=info_text,
                                 font=('Arial', 9), foreground='blue')
            info_label.pack(pady=(5, 10))

            # Create treeview with dynamic columns
            self.gads_validation_tree = ttk.Treeview(self.validation_container, columns=columns,
                                                   show='headings', height=15)

            for col in columns:
                self.gads_validation_tree.heading(col, text=col)
                if len(columns) == 3:  # Basic mode
                    self.gads_validation_tree.column(col, width=200, anchor=tk.CENTER)
                else:  # Full validation mode
                    self.gads_validation_tree.column(col, width=130, anchor=tk.CENTER)

            # Scrollbars
            validation_scroll_y = ttk.Scrollbar(self.validation_container, orient=tk.VERTICAL,
                                              command=self.gads_validation_tree.yview)
            validation_scroll_x = ttk.Scrollbar(self.validation_container, orient=tk.HORIZONTAL,
                                              command=self.gads_validation_tree.xview)
            self.gads_validation_tree.configure(yscrollcommand=validation_scroll_y.set,
                                              xscrollcommand=validation_scroll_x.set)

            self.gads_validation_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            validation_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
            validation_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        except Exception as e:
            print(f"Error setting up validation display: {e}")

    def get_filtered_data(self, data_source="current"):
        """Get filtered data based on current date filter settings"""
        try:
            if data_source == "current":
                data_source = self.current_data_source

            if data_source == "meta":
                data = self.raw_data
                date_column = 'Reporting ends'
            else:
                data = self.gads_raw_data
                date_column = 'Day'

            if data is None or not self.date_filter_active:
                return data

            # Apply date filter
            mask = (data[date_column].dt.date >= self.filter_start_date) & \
                   (data[date_column].dt.date <= self.filter_end_date)

            filtered_data = data[mask].copy()

            return filtered_data

        except Exception as e:
            print(f"Error filtering data: {e}")
            return data if 'data' in locals() else None

    def refresh_filtered_displays(self):
        """Refresh all displays with filtered data"""
        try:
            if self.current_data_source == "meta" and self.raw_data is not None:
                # Reprocess Meta Ads data with filters
                self.process_meta_ads_data()
                self.generate_meta_quality_report()
                self.update_meta_displays()
            elif self.current_data_source == "google" and self.gads_raw_data is not None:
                # Reprocess Google Ads data with filters
                self.process_google_ads_data()
                self.generate_gads_quality_report()
                self.update_gads_displays()

        except Exception as e:
            self.handle_error("Error refreshing filtered displays", e)

    def process_and_validate(self):
        """Process and validate data - THE WORKING VALIDATION BUTTON"""
        try:
            if self.current_data_source == "meta":
                if self.raw_data is None:
                    messagebox.showwarning("No Data", "Please load a Meta Ads CSV file first")
                    return

                self.status_var.set("Processing Meta Ads data...")
                self.root.update()

                # Process Meta Ads data
                self.process_meta_ads_data()
                self.generate_meta_quality_report()
                self.update_meta_displays()

                # Populate month dropdown with available months
                self.populate_month_dropdown()

                # Enable export buttons
                for btn in self.export_buttons:
                    btn.config(state=tk.NORMAL)

                self.status_var.set("Meta Ads data processed and validated successfully")

            else:  # Google Ads
                if self.gads_raw_data is None:
                    messagebox.showwarning("No Data", "Please load a Google Ads CSV file first")
                    return

                self.status_var.set("Processing Google Ads data...")
                self.root.update()

                # Process Google Ads data
                self.process_google_ads_data()
                self.generate_gads_quality_report()
                self.update_gads_displays()

                # Populate month dropdown with available months
                self.populate_month_dropdown()

                # Enable export buttons
                for btn in self.export_buttons:
                    btn.config(state=tk.NORMAL)

                self.status_var.set("Google Ads data processed and validated successfully")

        except Exception as e:
            self.handle_error("Error processing and validating data", e)

    def process_meta_ads_data(self):
        """Process Meta Ads data with all aggregations"""
        if self.raw_data is None:
            return

        try:
            # Get filtered data
            filtered_data = self.get_filtered_data("meta")
            if filtered_data is None or len(filtered_data) == 0:
                # Clear displays if no data in filter range
                self.ad_insights = None
                self.daily_aggregated = None
                self.monthly_summary = None
                return

            # Generate ad insights with filtered data
            self.generate_meta_ad_insights(filtered_data)

            # Generate aggregations if enabled
            if self.daily_aggregation.get():
                self.daily_aggregated = self.aggregate_meta_by_period(filtered_data, 'D')

            if self.monthly_summary_var.get():
                self.monthly_summary = self.aggregate_meta_by_period(filtered_data, 'M')

        except Exception as e:
            self.handle_error("Error processing Meta Ads data", e)

    def process_google_ads_data(self):
        """Process Google Ads data with all aggregations"""
        if self.gads_raw_data is None:
            return

        try:
            # Get filtered data
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Clear displays if no data in filter range
                self.gads_daily_aggregated = None
                self.gads_monthly_summary = None
                return

            # Generate aggregations if enabled
            if self.daily_aggregation.get():
                self.gads_daily_aggregated = self.aggregate_gads_by_period(filtered_data, 'D')

            if self.monthly_summary_var.get():
                self.gads_monthly_summary = self.aggregate_gads_by_period(filtered_data, 'M')

        except Exception as e:
            self.handle_error("Error processing Google Ads data", e)

    def aggregate_meta_by_period(self, df, period):
        """Enhanced Meta Ads aggregation"""
        try:
            agg_dict = {
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Ad name': 'count'
            }

            grouped = df.groupby(df['Reporting ends'].dt.to_period(period)).agg(agg_dict).reset_index()

            # Rename columns
            grouped = grouped.rename(columns={
                'Reporting ends': 'Period',
                'Amount spent (USD)': 'Total_Spend',
                'Results': 'Total_Results',
                'Reach': 'Total_Reach',
                'Impressions': 'Total_Impressions',
                'Ad name': 'Ad_Count'
            })

            # Calculate derived metrics
            grouped['Frequency'] = np.where(
                grouped['Total_Reach'] > 0,
                grouped['Total_Impressions'] / grouped['Total_Reach'],
                0
            )

            grouped['Cost_Per_Result'] = np.where(
                grouped['Total_Results'] > 0,
                grouped['Total_Spend'] / grouped['Total_Results'],
                0
            )

            # Format period
            grouped['Period'] = grouped['Period'].astype(str)

            return grouped

        except Exception as e:
            raise Exception(f"Error in Meta Ads aggregation: {str(e)}")

    def aggregate_gads_by_period(self, df, period):
        """Enhanced Google Ads aggregation"""
        try:
            agg_dict = {
                'Cost': 'sum',
                'Clicks': 'sum',
                'Impr.': 'sum',
                'Conversions': 'sum',
                'Campaign': 'count'
            }

            grouped = df.groupby(df['Day'].dt.to_period(period)).agg(agg_dict).reset_index()

            # Rename columns
            grouped = grouped.rename(columns={
                'Day': 'Period',
                'Cost': 'Total_Cost',
                'Clicks': 'Total_Clicks',
                'Impr.': 'Total_Impressions',
                'Conversions': 'Total_Conversions',
                'Campaign': 'Campaign_Count'
            })

            # Calculate derived metrics
            grouped['CTR'] = np.where(
                grouped['Total_Impressions'] > 0,
                grouped['Total_Clicks'] / grouped['Total_Impressions'],
                0
            )

            grouped['CPC'] = np.where(
                grouped['Total_Clicks'] > 0,
                grouped['Total_Cost'] / grouped['Total_Clicks'],
                0
            )

            grouped['Conv_Rate'] = np.where(
                grouped['Total_Impressions'] > 0,
                grouped['Total_Conversions'] / grouped['Total_Impressions'],
                0
            )

            # Format period
            grouped['Period'] = grouped['Period'].astype(str)

            return grouped

        except Exception as e:
            raise Exception(f"Error in Google Ads aggregation: {str(e)}")

    def generate_meta_ad_insights(self, data=None):
        """Generate comprehensive Meta Ads ad-level insights"""
        if data is None:
            data = self.raw_data

        if data is None:
            return

        try:
            # Ad-level analysis
            ad_analysis = data.groupby('Ad name').agg({
                'Amount spent (USD)': 'sum',
                'Results': 'sum',
                'Reach': 'sum',
                'Impressions': 'sum',
                'Reporting ends': ['min', 'max'],
                'Ad Set Name': 'first',
                'Ad delivery': 'first'
            }).reset_index()

            # Flatten column names
            ad_analysis.columns = ['Ad_Name', 'Total_Spend', 'Total_Results', 'Total_Reach',
                                 'Total_Impressions', 'Start_Date', 'End_Date', 'Campaign_Name', 'Status']

            # Calculate metrics
            ad_analysis['Cost_Per_Result'] = np.where(
                ad_analysis['Total_Results'] > 0,
                ad_analysis['Total_Spend'] / ad_analysis['Total_Results'],
                0
            )

            ad_analysis['Frequency'] = np.where(
                ad_analysis['Total_Reach'] > 0,
                ad_analysis['Total_Impressions'] / ad_analysis['Total_Reach'],
                0
            )

            # Calculate days active
            ad_analysis['Days_Active'] = (ad_analysis['End_Date'] - ad_analysis['Start_Date']).dt.days + 1

            # Add category based on performance
            ad_analysis['Category'] = np.where(
                ad_analysis['Cost_Per_Result'] <= ad_analysis['Cost_Per_Result'].median(),
                'High Performer',
                'Standard'
            )

            self.ad_insights = ad_analysis

        except Exception as e:
            self.handle_error("Error generating Meta Ads insights", e)

    def generate_meta_quality_report(self):
        """Generate comprehensive Meta Ads quality report"""
        if self.raw_data is None:
            return

        try:
            # Use filtered data for quality report
            filtered_data = self.get_filtered_data("meta")
            if filtered_data is None or len(filtered_data) == 0:
                # Show empty report if no data in filter range
                self.quality_text.config(state=tk.NORMAL)
                self.quality_text.delete(1.0, tk.END)
                self.quality_text.insert(1.0, "No data available for the selected date range.")
                self.quality_text.config(state=tk.DISABLED)
                return

            # Calculate metrics from filtered data
            filtered_report = {
                'initial_rows': len(self.raw_data),
                'valid_rows': len(filtered_data),
                'date_range': (filtered_data['Reporting ends'].min(), filtered_data['Reporting ends'].max()),
                'total_spend': filtered_data['Amount spent (USD)'].sum(),
                'total_results': filtered_data['Results'].sum(),
                'total_reach': filtered_data['Reach'].sum(),
                'total_impressions': filtered_data['Impressions'].sum(),
                'unique_ads': filtered_data['Ad name'].nunique() if 'Ad name' in filtered_data.columns else 0,
                'unique_ad_sets': filtered_data['Ad Set Name'].nunique() if 'Ad Set Name' in filtered_data.columns else 0,
                'delivery_statuses': filtered_data['Ad delivery'].value_counts().to_dict() if 'Ad delivery' in filtered_data.columns else {},
                'bid_types': filtered_data['Bid type'].value_counts().to_dict() if 'Bid type' in filtered_data.columns else {}
            }

            report = filtered_report

            # Calculate derived metrics
            cost_per_result = (report['total_spend'] / report['total_results']) if report['total_results'] > 0 else 0
            avg_frequency = (report['total_impressions'] / report['total_reach']) if report['total_reach'] > 0 else 0

            quality_text = f"""META ADS DATA QUALITY REPORT
{'='*80}

📊 DATA VALIDATION SUMMARY
• Initial rows loaded: {report['initial_rows']:,}
• Valid rows after cleaning: {report['valid_rows']:,}
• Data quality score: {(report['valid_rows']/report['initial_rows']*100):.1f}%

📅 DATE COVERAGE ANALYSIS
• Start date: {report['date_range'][0].strftime('%Y-%m-%d')}
• End date: {report['date_range'][1].strftime('%Y-%m-%d')}
• Total days covered: {(report['date_range'][1] - report['date_range'][0]).days + 1}

🎯 CAMPAIGN STRUCTURE OVERVIEW
• Unique ads: {report['unique_ads']:,}
• Unique ad sets: {report['unique_ad_sets']:,}
• Total spend: ${report['total_spend']:,.2f}
• Total results: {report['total_results']:,.0f}
• Cost per result: ${cost_per_result:.2f}

📈 PERFORMANCE METRICS
• Total reach: {report['total_reach']:,.0f}
• Total impressions: {report['total_impressions']:,.0f}
• Average frequency: {avg_frequency:.2f}

🚦 DELIVERY STATUS BREAKDOWN
"""

            # Add delivery status breakdown
            if report['delivery_statuses']:
                for status, count in report['delivery_statuses'].items():
                    percentage = (count / report['valid_rows'] * 100)
                    quality_text += f"• {status}: {count:,} ads ({percentage:.1f}%)\n"
            else:
                quality_text += "• No delivery status data available\n"

            quality_text += f"""
💰 BID STRATEGY ANALYSIS
"""
            # Add bid type breakdown
            if report['bid_types']:
                for bid_type, count in report['bid_types'].items():
                    if bid_type and bid_type != '0':
                        percentage = (count / report['valid_rows'] * 100)
                        quality_text += f"• {bid_type}: {count:,} ads ({percentage:.1f}%)\n"
            else:
                quality_text += "• No bid type data available\n"

            # Update quality display
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, quality_text)
            self.quality_text.config(state=tk.DISABLED)

        except Exception as e:
            self.handle_error("Error generating Meta Ads quality report", e)

    def generate_gads_quality_report(self):
        """Generate comprehensive Google Ads quality report"""
        if self.gads_raw_data is None:
            return

        try:
            # Use filtered data for quality report
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Show empty report if no data in filter range
                self.quality_text.config(state=tk.NORMAL)
                self.quality_text.delete(1.0, tk.END)
                self.quality_text.insert(1.0, "No Google Ads data available for the selected date range.")
                self.quality_text.config(state=tk.DISABLED)
                return

            # Calculate metrics from filtered data
            filtered_report = {
                'initial_rows': len(self.gads_raw_data),
                'valid_rows': len(filtered_data),
                'date_range': (filtered_data['Day'].min(), filtered_data['Day'].max()),
                'total_cost': filtered_data['Cost'].sum(),
                'total_clicks': filtered_data['Clicks'].sum(),
                'total_impressions': filtered_data['Impr.'].sum(),
                'total_conversions': filtered_data['Conversions'].sum(),
                'unique_campaigns': filtered_data['Campaign'].nunique(),
                'avg_ctr': filtered_data['CTR'].mean() if 'CTR' in filtered_data.columns else 0,
                'avg_cpc': filtered_data['CPC'].mean() if 'CPC' in filtered_data.columns else 0
            }

            report = filtered_report

            # Calculate derived metrics
            cost_per_conversion = (report['total_cost'] / report['total_conversions']) if report['total_conversions'] > 0 else 0

            quality_text = f"""GOOGLE ADS DATA QUALITY REPORT
{'='*80}

📊 DATA VALIDATION SUMMARY
• Initial rows loaded: {report['initial_rows']:,}
• Valid rows after cleaning: {report['valid_rows']:,}
• Data quality score: {(report['valid_rows']/report['initial_rows']*100):.1f}%

📅 DATE COVERAGE ANALYSIS
• Start date: {report['date_range'][0].strftime('%Y-%m-%d')}
• End date: {report['date_range'][1].strftime('%Y-%m-%d')}
• Total days covered: {(report['date_range'][1] - report['date_range'][0]).days + 1}

🎯 CAMPAIGN PERFORMANCE OVERVIEW
• Unique campaigns: {report['unique_campaigns']:,}
• Total cost: ${report['total_cost']:,.2f}
• Total clicks: {report['total_clicks']:,.0f}
• Total conversions: {report['total_conversions']:,.0f}
• Cost per conversion: ${cost_per_conversion:.2f}

📈 PERFORMANCE METRICS
• Total impressions: {report['total_impressions']:,.0f}
• Average CTR: {(report['avg_ctr']*100):.2f}%
• Average CPC: ${report['avg_cpc']:.2f}
• Conversion rate: {(report['total_conversions']/report['total_clicks']*100):.2f}%
"""

            # Update quality display
            self.quality_text.config(state=tk.NORMAL)
            self.quality_text.delete(1.0, tk.END)
            self.quality_text.insert(1.0, quality_text)
            self.quality_text.config(state=tk.DISABLED)

        except Exception as e:
            self.handle_error("Error generating Google Ads quality report", e)

    def update_displays_for_source(self):
        """Update displays when switching data sources"""
        # Clear all displays first
        self.quality_text.config(state=tk.NORMAL)
        self.quality_text.delete(1.0, tk.END)
        self.quality_text.insert(1.0, f"Load {self.current_data_source.title()} Ads data to see quality report")
        self.quality_text.config(state=tk.DISABLED)

        # Setup appropriate tabs
        self.setup_all_data_tabs()

        # Disable validate button until data is loaded
        self.validate_btn.config(state=tk.DISABLED)

        # Disable export buttons
        for btn in self.export_buttons:
            btn.config(state=tk.DISABLED)

    def update_meta_displays(self):
        """Update all Meta Ads displays"""
        try:
            self.update_meta_ad_insights_display()
            self.update_meta_ad_performance_display()
            self.update_meta_airtable_display()
            self.update_meta_summary_validation()
            self.update_meta_monthly_display()
            self.update_meta_daily_display()
            self.update_meta_cleaned_data_display()

        except Exception as e:
            self.handle_error("Error updating Meta Ads displays", e)

    def update_gads_displays(self):
        """Update all Google Ads displays"""
        try:
            self.update_gads_campaign_display()
            self.update_gads_validation_display()
            self.update_gads_deep_dive_display()
            self.update_gads_temporal_display()
            self.update_gads_monthly_display()
            self.update_gads_raw_display()

        except Exception as e:
            self.handle_error("Error updating Google Ads displays", e)

    def apply_alternating_row_colors(self, tree):
        """Apply alternating row colors to a treeview for better readability"""
        try:
            children = tree.get_children()
            for i, child in enumerate(children):
                if i % 2 == 0:
                    tree.item(child, tags=('evenrow',))
                else:
                    tree.item(child, tags=('oddrow',))
        except Exception as e:
            print(f"Error applying alternating row colors: {e}")

    def setup_button_hover_effects(self, button, hover_bg, hover_fg):
        """Setup manual hover effects for buttons to ensure they work properly"""
        try:
            # Store original colors
            original_bg = button.cget('background') if hasattr(button, 'cget') else '#5294e2'
            original_fg = button.cget('foreground') if hasattr(button, 'cget') else 'white'

            def on_enter(event):
                try:
                    button.configure(background=hover_bg, foreground=hover_fg)
                except:
                    pass

            def on_leave(event):
                try:
                    button.configure(background=original_bg, foreground=original_fg)
                except:
                    pass

            # Bind hover events
            button.bind('<Enter>', on_enter)
            button.bind('<Leave>', on_leave)

        except Exception as e:
            print(f"Error setting up button hover effects: {e}")

    def update_meta_ad_insights_display(self):
        """Update Meta Ads insights display"""
        if not hasattr(self, 'insights_tree') or self.ad_insights is None:
            return

        # Clear existing data
        for item in self.insights_tree.get_children():
            self.insights_tree.delete(item)

        for _, row in self.ad_insights.head(100).iterrows():
            values = (
                str(row['Ad_Name'])[:30],
                str(row['Campaign_Name'])[:20],
                f"${row['Total_Spend']:,.2f}",
                f"{row['Total_Results']:,.0f}",
                f"{row['Total_Reach']:,.0f}",
                f"{row['Total_Impressions']:,.0f}",
                f"{row['Frequency']:.2f}",
                f"${row['Cost_Per_Result']:.2f}",
                str(row['Status']),
                row['Start_Date'].strftime('%Y-%m-%d') if pd.notnull(row['Start_Date']) else 'N/A',
                row['End_Date'].strftime('%Y-%m-%d') if pd.notnull(row['End_Date']) else 'N/A',
                f"{row['Days_Active']:,}",
                str(row['Category'])
            )
            self.insights_tree.insert('', 'end', values=values)

        # Apply alternating row colors for better readability
        self.apply_alternating_row_colors(self.insights_tree)

    def update_meta_monthly_display(self):
        """Update Meta Ads monthly display"""
        if not hasattr(self, 'monthly_tree') or self.monthly_summary is None:
            return

        # Clear existing data
        for item in self.monthly_tree.get_children():
            self.monthly_tree.delete(item)

        for _, row in self.monthly_summary.iterrows():
            values = (
                row['Period'],
                f"${row['Total_Spend']:,.2f}",
                f"{row['Total_Results']:,.0f}",
                f"{row['Total_Reach']:,.0f}",
                f"{row['Total_Impressions']:,.0f}",
                f"{row['Frequency']:.2f}",
                f"${row['Cost_Per_Result']:.2f}",
                f"{row['Ad_Count']:,}"
            )
            self.monthly_tree.insert('', 'end', values=values)

        # Apply alternating row colors for better readability
        self.apply_alternating_row_colors(self.monthly_tree)

    def update_meta_daily_display(self):
        """Update Meta Ads daily display"""
        if not hasattr(self, 'daily_tree') or self.daily_aggregated is None:
            return

        # Clear existing data
        for item in self.daily_tree.get_children():
            self.daily_tree.delete(item)

        for _, row in self.daily_aggregated.head(100).iterrows():
            values = (
                row['Period'],
                f"${row['Total_Spend']:,.2f}",
                f"{row['Total_Results']:,.0f}",
                f"{row['Total_Reach']:,.0f}",
                f"{row['Total_Impressions']:,.0f}",
                f"{row['Frequency']:.2f}",
                f"${row['Cost_Per_Result']:.2f}",
                f"{row['Ad_Count']:,}"
            )
            self.daily_tree.insert('', 'end', values=values)

        # Apply alternating row colors for better readability
        self.apply_alternating_row_colors(self.daily_tree)

    def update_meta_cleaned_data_display(self):
        """Update Meta Ads cleaned data display"""
        if not hasattr(self, 'cleaned_tree') or self.raw_data is None:
            return

        # Clear existing data
        for item in self.cleaned_tree.get_children():
            self.cleaned_tree.delete(item)

        # Show first 50 rows with all available columns
        display_data = self.raw_data.head(50)
        for _, row in display_data.iterrows():
            values = []
            for col in self.cleaned_tree['columns']:
                if col in self.raw_data.columns:
                    if pd.api.types.is_datetime64_any_dtype(self.raw_data[col]):
                        val = row[col].strftime('%Y-%m-%d') if pd.notnull(row[col]) else 'N/A'
                    elif pd.api.types.is_numeric_dtype(self.raw_data[col]):
                        val = f"{row[col]:,.2f}" if pd.notnull(row[col]) else '0'
                    else:
                        val = str(row[col])[:15] if pd.notnull(row[col]) else 'N/A'
                    values.append(val)
                else:
                    values.append('N/A')
            self.cleaned_tree.insert('', 'end', values=values)

    def update_gads_campaign_display(self):
        """Update Google Ads campaign display"""
        if not hasattr(self, 'gads_campaign_tree') or self.gads_raw_data is None:
            return

        # Clear existing data
        for item in self.gads_campaign_tree.get_children():
            self.gads_campaign_tree.delete(item)

        # Campaign-level analysis
        campaign_analysis = self.gads_raw_data.groupby('Campaign').agg({
            'Cost': 'sum',
            'Clicks': 'sum',
            'Impr.': 'sum',
            'Conversions': 'sum'
        }).reset_index()

        # Calculate metrics
        campaign_analysis['CTR'] = np.where(
            campaign_analysis['Impr.'] > 0,
            campaign_analysis['Clicks'] / campaign_analysis['Impr.'],
            0
        )

        campaign_analysis['CPC'] = np.where(
            campaign_analysis['Clicks'] > 0,
            campaign_analysis['Cost'] / campaign_analysis['Clicks'],
            0
        )

        campaign_analysis['Conv_Rate'] = np.where(
            campaign_analysis['Clicks'] > 0,
            campaign_analysis['Conversions'] / campaign_analysis['Clicks'],
            0
        )

        campaign_analysis['Cost_Per_Conv'] = np.where(
            campaign_analysis['Conversions'] > 0,
            campaign_analysis['Cost'] / campaign_analysis['Conversions'],
            0
        )

        # ROI Score (simple efficiency metric)
        campaign_analysis['ROI_Score'] = np.where(
            campaign_analysis['Cost'] > 0,
            campaign_analysis['Conversions'] / campaign_analysis['Cost'] * 100,
            0
        )

        for _, row in campaign_analysis.head(50).iterrows():
            values = (
                str(row['Campaign'])[:25],
                f"${row['Cost']:,.2f}",
                f"{row['Clicks']:,.0f}",
                f"{row['Impr.']:,.0f}",
                f"{row['Conversions']:,.0f}",
                f"{row['CTR']*100:.2f}%",
                f"${row['CPC']:.2f}",
                f"{row['Conv_Rate']*100:.2f}%",
                f"${row['Cost_Per_Conv']:.2f}",
                f"{row['ROI_Score']:.1f}"
            )
            self.gads_campaign_tree.insert('', 'end', values=values)

        # Update summary totals
        self.update_gads_summary_totals()

    def update_gads_monthly_display(self):
        """Update Google Ads monthly display with filtering support"""
        if not hasattr(self, 'gads_monthly_tree') or self.gads_monthly_summary is None:
            return

        # Clear existing data
        for item in self.gads_monthly_tree.get_children():
            self.gads_monthly_tree.delete(item)

        try:
            # The monthly summary is already filtered, so just display it
            for _, row in self.gads_monthly_summary.iterrows():
                values = (
                    row['Period'],
                    f"${row['Total_Cost']:,.2f}",
                    f"{row['Total_Clicks']:,.0f}",
                    f"{row['Total_Impressions']:,.0f}",
                    f"{row['Total_Conversions']:,.0f}",
                    f"{row['CTR']*100:.2f}%",
                    f"${row['CPC']:.2f}",
                    f"{row['Conv_Rate']*100:.2f}%"
                )
                self.gads_monthly_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in monthly display: {e}")

        # Update summary totals
        self.update_gads_summary_totals()

    def update_gads_raw_display(self):
        """Update Google Ads raw data display with filtered data"""
        if not hasattr(self, 'gads_raw_tree') or self.gads_raw_data is None:
            return

        # Clear existing data
        for item in self.gads_raw_tree.get_children():
            self.gads_raw_tree.delete(item)

        try:
            # Use filtered data for raw display
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Show message if no data in filter range
                values = ("No data in range", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A")
                self.gads_raw_tree.insert('', 'end', values=values)
                return

            # Display filtered data (show all filtered records, or limit to 5000 for very large datasets)
            if len(filtered_data) <= 5000:
                display_data = filtered_data
            else:
                display_data = filtered_data.head(5000)
                print(f"Note: Showing first 5000 of {len(filtered_data)} filtered records")

            for _, row in display_data.iterrows():
                values = (
                    row['Day'].strftime('%Y-%m-%d') if pd.notnull(row['Day']) else 'N/A',
                    str(row['Campaign'])[:20],
                    f"${row['Cost']:,.2f}" if pd.notnull(row['Cost']) else '$0.00',
                    f"{row['Clicks']:,.0f}" if pd.notnull(row['Clicks']) else '0',
                    f"{row['Impr.']:,.0f}" if pd.notnull(row['Impr.']) else '0',
                    f"{row['Conversions']:,.0f}" if pd.notnull(row['Conversions']) else '0',
                    f"{row.get('CTR', 0)*100:.2f}%" if 'CTR' in row and pd.notnull(row['CTR']) else '0.00%',
                    f"${row.get('CPC', 0):.2f}" if 'CPC' in row and pd.notnull(row['CPC']) else '$0.00'
                )
                self.gads_raw_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in raw data display: {e}")

        # Update summary totals
        self.update_gads_summary_totals()

    def update_gads_summary_totals(self):
        """Update Google Ads summary totals for all relevant tabs"""
        try:
            # Get filtered data for calculations
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Clear totals if no data
                self.clear_gads_summary_totals()
                return

            # Calculate totals from filtered data
            total_cost = filtered_data['Cost'].sum()
            total_clicks = filtered_data['Clicks'].sum()
            total_impressions = filtered_data['Impr.'].sum()
            total_conversions = filtered_data['Conversions'].sum()

            # Update Raw Data summary totals
            if hasattr(self, 'gads_summary_labels'):
                self.gads_summary_labels['total_cost'].config(text=f"${total_cost:,.2f}")
                self.gads_summary_labels['total_clicks'].config(text=f"{total_clicks:,.0f}")
                self.gads_summary_labels['total_impressions'].config(text=f"{total_impressions:,.0f}")
                self.gads_summary_labels['total_conversions'].config(text=f"{total_conversions:,.0f}")

            # Update Campaign Performance summary totals (same data, different view)
            if hasattr(self, 'gads_campaign_summary_labels'):
                self.gads_campaign_summary_labels['campaign_total_cost'].config(text=f"${total_cost:,.2f}")
                self.gads_campaign_summary_labels['campaign_total_clicks'].config(text=f"{total_clicks:,.0f}")
                self.gads_campaign_summary_labels['campaign_total_impressions'].config(text=f"{total_impressions:,.0f}")
                self.gads_campaign_summary_labels['campaign_total_conversions'].config(text=f"{total_conversions:,.0f}")

            # Update Monthly Summary totals (aggregated data)
            if hasattr(self, 'gads_monthly_summary_labels') and self.gads_monthly_summary is not None:
                # The monthly summary is already filtered, so just sum its columns
                if not self.gads_monthly_summary.empty:
                    monthly_total_cost = self.gads_monthly_summary['Total_Cost'].sum()
                    monthly_total_clicks = self.gads_monthly_summary['Total_Clicks'].sum()
                    monthly_total_impressions = self.gads_monthly_summary['Total_Impressions'].sum()
                    monthly_total_conversions = self.gads_monthly_summary['Total_Conversions'].sum()

                    self.gads_monthly_summary_labels['monthly_total_cost'].config(text=f"${monthly_total_cost:,.2f}")
                    self.gads_monthly_summary_labels['monthly_total_clicks'].config(text=f"{monthly_total_clicks:,.0f}")
                    self.gads_monthly_summary_labels['monthly_total_impressions'].config(text=f"{monthly_total_impressions:,.0f}")
                    self.gads_monthly_summary_labels['monthly_total_conversions'].config(text=f"{monthly_total_conversions:,.0f}")
                else:
                    self.gads_monthly_summary_labels['monthly_total_cost'].config(text="$0.00")
                    self.gads_monthly_summary_labels['monthly_total_clicks'].config(text="0")
                    self.gads_monthly_summary_labels['monthly_total_impressions'].config(text="0")
                    self.gads_monthly_summary_labels['monthly_total_conversions'].config(text="0")

        except Exception as e:
            print(f"Error updating Google Ads summary totals: {e}")

    def clear_gads_summary_totals(self):
        """Clear Google Ads summary totals when no data available"""
        try:
            # Clear Raw Data totals
            if hasattr(self, 'gads_summary_labels'):
                for label in self.gads_summary_labels.values():
                    label.config(text="$0.00")

            # Clear Campaign Performance totals
            if hasattr(self, 'gads_campaign_summary_labels'):
                for label in self.gads_campaign_summary_labels.values():
                    label.config(text="$0.00")

            # Clear Monthly Summary totals
            if hasattr(self, 'gads_monthly_summary_labels'):
                for label in self.gads_monthly_summary_labels.values():
                    label.config(text="$0.00")

        except Exception as e:
            print(f"Error clearing Google Ads summary totals: {e}")

    def update_gads_validation_display(self):
        """Update Google Ads dashboard validation display with dynamic columns"""
        if not hasattr(self, 'gads_validation_tree') or self.gads_raw_data is None:
            return

        # Clear existing data
        for item in self.gads_validation_tree.get_children():
            self.gads_validation_tree.delete(item)

        try:
            # Use filtered data for validation
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Show message if no data in filter range
                if self.dashboard_file_loaded:
                    values = ("No data", "N/A", "N/A", "N/A", "N/A", "No data in range", "N/A")
                else:
                    values = ("No data", "N/A", "No data in range")
                self.gads_validation_tree.insert('', 'end', values=values)
                return

            # Calculate actual metrics from filtered CSV data
            csv_metrics = {
                'Total Cost': filtered_data['Cost'].sum(),
                'Total Clicks': filtered_data['Clicks'].sum(),
                'Total Impressions': filtered_data['Impr.'].sum(),
                'Total Conversions': filtered_data['Conversions'].sum(),
                'Average CTR': filtered_data['CTR'].mean() if 'CTR' in filtered_data.columns else 0,
                'Average CPC': filtered_data['CPC'].mean() if 'CPC' in filtered_data.columns else 0,
                'Conversion Rate': (filtered_data['Conversions'].sum() / filtered_data['Clicks'].sum()) if filtered_data['Clicks'].sum() > 0 else 0,
                'Cost per Conversion': (filtered_data['Cost'].sum() / filtered_data['Conversions'].sum()) if filtered_data['Conversions'].sum() > 0 else 0
            }

            if self.dashboard_file_loaded and self.dashboard_data is not None:
                # Full validation mode with dashboard comparison
                self.display_full_validation(csv_metrics)
            else:
                # Basic mode - show only CSV data
                self.display_basic_validation(csv_metrics)

        except Exception as e:
            print(f"Error in validation display: {e}")

    def display_full_validation(self, csv_metrics):
        """Display full validation with dashboard comparison"""
        try:
            # Extract dashboard metrics from loaded data
            # This assumes dashboard data has columns matching our metrics
            dashboard_metrics = {}

            if 'Total Cost' in self.dashboard_data.columns:
                dashboard_metrics['Total Cost'] = self.dashboard_data['Total Cost'].sum()
            if 'Total Clicks' in self.dashboard_data.columns:
                dashboard_metrics['Total Clicks'] = self.dashboard_data['Total Clicks'].sum()
            # Add more mappings as needed based on dashboard data structure

            # If no matching columns found, show message
            if not dashboard_metrics:
                values = ("Dashboard Format", "Unknown format", "Please check dashboard data structure",
                         "N/A", "N/A", "⚠ Format Issue", "Needs Review")
                self.gads_validation_tree.insert('', 'end', values=values)
                return

            # Compare metrics
            for metric_name in csv_metrics.keys():
                csv_value = csv_metrics[metric_name]
                dashboard_value = dashboard_metrics.get(metric_name, 0)

                if dashboard_value == 0:
                    # Metric not available in dashboard data
                    values = (metric_name, "N/A", self.format_metric_value(metric_name, csv_value),
                             "N/A", "N/A", "⚠ No Dashboard Data", "Partial")
                else:
                    # Calculate difference and variance
                    difference = csv_value - dashboard_value
                    variance_pct = (difference / dashboard_value * 100) if dashboard_value != 0 else 0

                    # Determine status
                    if abs(variance_pct) <= 1:
                        status = "✓ Match"
                        quality = "Excellent"
                    elif abs(variance_pct) <= 5:
                        status = "⚠ Minor Diff"
                        quality = "Good"
                    else:
                        status = "✗ Mismatch"
                        quality = "Needs Review"

                    values = (
                        metric_name,
                        self.format_metric_value(metric_name, dashboard_value),
                        self.format_metric_value(metric_name, csv_value),
                        self.format_metric_difference(metric_name, difference),
                        f"{variance_pct:.2f}%",
                        status,
                        quality
                    )

                self.gads_validation_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in full validation display: {e}")

    def display_basic_validation(self, csv_metrics):
        """Display basic validation showing only CSV data"""
        try:
            for metric_name, csv_value in csv_metrics.items():
                # Determine data quality based on value reasonableness
                if csv_value > 0:
                    quality = "Good"
                elif csv_value == 0:
                    quality = "Zero Value"
                else:
                    quality = "Invalid"

                values = (
                    metric_name,
                    self.format_metric_value(metric_name, csv_value),
                    quality
                )
                self.gads_validation_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in basic validation display: {e}")

    def format_metric_value(self, metric_name, value):
        """Format metric value for display"""
        if metric_name in ['Total Cost', 'Average CPC', 'Cost per Conversion']:
            return f"${value:,.2f}"
        elif metric_name in ['Average CTR', 'Conversion Rate']:
            return f"{value*100:.2f}%"
        else:
            return f"{value:,.0f}"

    def format_metric_difference(self, metric_name, difference):
        """Format metric difference for display"""
        if metric_name in ['Total Cost', 'Average CPC', 'Cost per Conversion']:
            return f"${difference:,.2f}"
        elif metric_name in ['Average CTR', 'Conversion Rate']:
            return f"{difference*100:.2f}%"
        else:
            return f"{difference:,.0f}"

    def update_gads_deep_dive_display(self):
        """Update Google Ads campaign deep dive display"""
        if not hasattr(self, 'gads_deep_dive_tree') or self.gads_raw_data is None:
            return

        # Clear existing data
        for item in self.gads_deep_dive_tree.get_children():
            self.gads_deep_dive_tree.delete(item)

        try:
            # Use filtered data for deep dive analysis
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Show message if no data in filter range
                values = ("No data in range", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A")
                self.gads_deep_dive_tree.insert('', 'end', values=values)
                return

            # Campaign-level analysis with filtered data
            campaign_analysis = filtered_data.groupby('Campaign').agg({
                'Cost': 'sum',
                'Clicks': 'sum',
                'Impr.': 'sum',
                'Conversions': 'sum',
                'Day': ['min', 'max', 'count']
            }).reset_index()

            # Flatten column names
            campaign_analysis.columns = ['Campaign', 'Total_Cost', 'Total_Clicks', 'Total_Impressions',
                                       'Total_Conversions', 'Start_Date', 'End_Date', 'Days_Active']

            # Calculate advanced metrics
            campaign_analysis['Conv_Rate'] = np.where(
                campaign_analysis['Total_Impressions'] > 0,
                campaign_analysis['Total_Conversions'] / campaign_analysis['Total_Impressions'],
                0
            )

            campaign_analysis['Cost_Per_Conv'] = np.where(
                campaign_analysis['Total_Conversions'] > 0,
                campaign_analysis['Total_Cost'] / campaign_analysis['Total_Conversions'],
                0
            )

            # Performance scoring (0-100)
            median_cost_per_conv = campaign_analysis['Cost_Per_Conv'].median()
            median_conv_rate = campaign_analysis['Conv_Rate'].median()

            for _, row in campaign_analysis.head(50).iterrows():
                # Performance score calculation
                cost_score = max(0, min(100, 100 - (row['Cost_Per_Conv'] / median_cost_per_conv - 1) * 50)) if median_cost_per_conv > 0 else 50
                conv_score = max(0, min(100, (row['Conv_Rate'] / median_conv_rate) * 50)) if median_conv_rate > 0 else 50
                performance_score = (cost_score + conv_score) / 2

                # Efficiency rating
                if performance_score >= 80:
                    efficiency = "Excellent"
                elif performance_score >= 60:
                    efficiency = "Good"
                elif performance_score >= 40:
                    efficiency = "Average"
                else:
                    efficiency = "Needs Improvement"

                # Lifecycle stage
                if row['Days_Active'] <= 7:
                    lifecycle = "Launch"
                elif row['Days_Active'] <= 30:
                    lifecycle = "Optimization"
                else:
                    lifecycle = "Mature"

                # Optimization opportunity
                if row['Conv_Rate'] < median_conv_rate * 0.8:
                    opportunity = "Improve Targeting"
                elif row['Cost_Per_Conv'] > median_cost_per_conv * 1.2:
                    opportunity = "Reduce Costs"
                else:
                    opportunity = "Scale Up"

                # Budget utilization (simplified)
                budget_util = min(100, (row['Total_Cost'] / (row['Days_Active'] * 50)) * 100)  # Assuming $50/day budget

                # Trend direction (simplified)
                trend = "Stable"  # Would need time series analysis for actual trend

                values = (
                    str(row['Campaign'])[:25],
                    f"{performance_score:.1f}",
                    efficiency,
                    lifecycle,
                    opportunity,
                    f"{budget_util:.1f}%",
                    trend,
                    "N/A"  # Quality score would need additional data
                )
                self.gads_deep_dive_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in deep dive display: {e}")

    def update_gads_temporal_display(self):
        """Update Google Ads temporal analysis display"""
        if not hasattr(self, 'gads_temporal_tree') or self.gads_raw_data is None:
            return

        # Clear existing data
        for item in self.gads_temporal_tree.get_children():
            self.gads_temporal_tree.delete(item)

        try:
            # Use filtered data for temporal analysis
            filtered_data = self.get_filtered_data("google")
            if filtered_data is None or len(filtered_data) == 0:
                # Show message if no data in filter range
                values = ("No data in range", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A", "N/A")
                self.gads_temporal_tree.insert('', 'end', values=values)
                return

            # Daily performance analysis with filtered data
            daily_analysis = filtered_data.groupby('Day').agg({
                'Cost': 'sum',
                'Clicks': 'sum',
                'Impr.': 'sum',
                'Conversions': 'sum'
            }).reset_index()

            # Calculate daily metrics
            daily_analysis['Conv_Rate'] = np.where(
                daily_analysis['Clicks'] > 0,
                daily_analysis['Conversions'] / daily_analysis['Clicks'],
                0
            )

            daily_analysis['Cost_Per_Conv'] = np.where(
                daily_analysis['Conversions'] > 0,
                daily_analysis['Cost'] / daily_analysis['Conversions'],
                0
            )

            # Calculate performance velocity (rate of change)
            daily_analysis = daily_analysis.sort_values('Day')
            daily_analysis['Conv_Rate_Change'] = daily_analysis['Conv_Rate'].pct_change()
            daily_analysis['Cost_Change'] = daily_analysis['Cost'].pct_change()

            # Anomaly detection (simplified)
            conv_rate_std = daily_analysis['Conv_Rate'].std()
            conv_rate_mean = daily_analysis['Conv_Rate'].mean()

            for _, row in daily_analysis.head(30).iterrows():  # Show last 30 days
                # Performance velocity
                velocity = row['Conv_Rate_Change'] if pd.notnull(row['Conv_Rate_Change']) else 0
                velocity_display = f"{velocity*100:.1f}%"

                # Trend direction
                if velocity > 0.05:
                    trend = "📈 Improving"
                elif velocity < -0.05:
                    trend = "📉 Declining"
                else:
                    trend = "➡️ Stable"

                # Anomaly score
                if conv_rate_std > 0:
                    anomaly_score = abs(row['Conv_Rate'] - conv_rate_mean) / conv_rate_std
                else:
                    anomaly_score = 0

                # Pattern type
                if anomaly_score > 2:
                    pattern = "Outlier"
                elif velocity > 0.1:
                    pattern = "Growth Spike"
                elif velocity < -0.1:
                    pattern = "Performance Drop"
                else:
                    pattern = "Normal"

                # Risk level
                if anomaly_score > 2 and velocity < 0:
                    risk = "High"
                elif anomaly_score > 1:
                    risk = "Medium"
                else:
                    risk = "Low"

                values = (
                    row['Day'].strftime('%Y-%m-%d'),
                    velocity_display,
                    trend,
                    f"{anomaly_score:.2f}",
                    pattern,
                    "N/A",  # Forecast accuracy would need historical data
                    "N/A",  # Optimization window would need more analysis
                    risk
                )
                self.gads_temporal_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"Error in temporal display: {e}")

    def update_meta_ad_performance_display(self):
        """Update Meta Ads performance display"""
        if not hasattr(self, 'performance_tree') or self.ad_insights is None:
            return

        # Clear existing data
        for item in self.performance_tree.get_children():
            self.performance_tree.delete(item)

        for _, row in self.ad_insights.head(100).iterrows():
            # Calculate performance score (0-100 based on cost efficiency)
            median_cost = self.ad_insights['Cost_Per_Result'].median()
            performance_score = max(0, min(100, 100 - (row['Cost_Per_Result'] / median_cost - 1) * 50))

            # Efficiency rating
            if performance_score >= 80:
                efficiency = "Excellent"
            elif performance_score >= 60:
                efficiency = "Good"
            elif performance_score >= 40:
                efficiency = "Average"
            else:
                efficiency = "Needs Improvement"

            values = (
                str(row['Ad_Name'])[:25],
                str(row['Campaign_Name'])[:20],
                f"${row['Total_Spend']:,.2f}",
                f"{row['Total_Results']:,.0f}",
                f"{row['Total_Reach']:,.0f}",
                f"{row['Total_Impressions']:,.0f}",
                f"${row['Cost_Per_Result']:.2f}",
                f"{row['Frequency']:.2f}",
                f"{performance_score:.1f}",
                efficiency
            )
            self.performance_tree.insert('', 'end', values=values)

        # Apply alternating row colors for better readability
        self.apply_alternating_row_colors(self.performance_tree)

    def update_meta_airtable_display(self):
        """Update Meta Ads Airtable export preview"""
        if not hasattr(self, 'airtable_tree') or self.raw_data is None:
            return

        # Clear existing data
        for item in self.airtable_tree.get_children():
            self.airtable_tree.delete(item)

        # Show sample for Airtable format
        display_data = self.raw_data.head(50)
        for _, row in display_data.iterrows():
            values = (
                row['Reporting ends'].strftime('%Y-%m-%d') if pd.notnull(row['Reporting ends']) else 'N/A',
                str(row.get('Ad name', 'N/A'))[:25],
                str(row.get('Ad Set Name', 'N/A'))[:20],
                f"${row['Amount spent (USD)']:,.2f}" if pd.notnull(row['Amount spent (USD)']) else '$0.00',
                f"{row['Results']:,.0f}" if pd.notnull(row['Results']) else '0',
                f"{row['Reach']:,.0f}" if pd.notnull(row['Reach']) else '0',
                f"{row['Impressions']:,.0f}" if pd.notnull(row['Impressions']) else '0'
            )
            self.airtable_tree.insert('', 'end', values=values)

        # Apply alternating row colors for better readability
        self.apply_alternating_row_colors(self.airtable_tree)

    def update_meta_summary_validation(self):
        """Update Meta Ads summary validation comparison"""
        if not hasattr(self, 'summary_tree') or self.raw_data is None:
            return

        # Clear existing data
        for item in self.summary_tree.get_children():
            self.summary_tree.delete(item)

        try:
            # This would compare Meta's summary row vs our calculations
            # For now, we'll show our calculations vs expected values
            report = self.data_quality_report

            summary_data = [
                ('Total Spend', f"${report['total_spend']:,.2f}",
                 f"${report['total_spend']:,.2f}", '$0.00', '0.0%', '✓ Match'),
                ('Total Results', f"{report['total_results']:,.0f}",
                 f"{report['total_results']:,.0f}", '0', '0.0%', '✓ Match'),
                ('Total Reach', f"{report['total_reach']:,.0f}",
                 f"{report['total_reach']:,.0f}", '0', '0.0%', '✓ Match'),
                ('Total Impressions', f"{report['total_impressions']:,.0f}",
                 f"{report['total_impressions']:,.0f}", '0', '0.0%', '✓ Match')
            ]

            for row_data in summary_data:
                self.summary_tree.insert('', 'end', values=row_data)

            # Apply alternating row colors for better readability
            self.apply_alternating_row_colors(self.summary_tree)

        except Exception as e:
            print(f"Error in summary validation: {e}")

    # Export Functions
    def export_airtable(self):
        """Export data in Airtable format"""
        if self.current_data_source == "meta":
            if self.raw_data is None:
                messagebox.showwarning("No Data", "Please load Meta Ads data first")
                return
            self.export_data(self.raw_data, "meta_airtable_export")
        else:
            if self.gads_raw_data is None:
                messagebox.showwarning("No Data", "Please load Google Ads data first")
                return
            self.export_data(self.gads_raw_data, "gads_airtable_export")

    def export_ad_performance(self):
        """Export ad performance data"""
        if self.current_data_source == "meta":
            if self.ad_insights is None:
                messagebox.showwarning("No Data", "Please process Meta Ads data first")
                return
            self.export_data(self.ad_insights, "meta_ad_performance")
        else:
            messagebox.showwarning("Not Available", "Ad performance export is specific to Meta Ads")

    def export_ad_insights(self):
        """Export ad insights data"""
        if self.current_data_source == "meta":
            if self.ad_insights is None:
                messagebox.showwarning("No Data", "Please process Meta Ads data first")
                return
            self.export_data(self.ad_insights, "meta_ad_insights")
        else:
            messagebox.showwarning("Not Available", "Ad insights export is specific to Meta Ads")

    def export_daily(self):
        """Export daily aggregated data"""
        if self.current_data_source == "meta":
            if self.daily_aggregated is None:
                messagebox.showwarning("No Data", "Please process Meta Ads data first")
                return
            self.export_data(self.daily_aggregated, "meta_daily_aggregated")
        else:
            if self.gads_daily_aggregated is None:
                messagebox.showwarning("No Data", "Please process Google Ads data first")
                return
            self.export_data(self.gads_daily_aggregated, "gads_daily_aggregated")

    def export_monthly(self):
        """Export monthly summary data"""
        if self.current_data_source == "meta":
            if self.monthly_summary is None:
                messagebox.showwarning("No Data", "Please process Meta Ads data first")
                return
            self.export_data(self.monthly_summary, "meta_monthly_summary")
        else:
            if self.gads_monthly_summary is None:
                messagebox.showwarning("No Data", "Please process Google Ads data first")
                return
            self.export_data(self.gads_monthly_summary, "gads_monthly_summary")

    def export_validation_report(self):
        """Export validation report"""
        if (self.current_data_source == "meta" and self.raw_data is None) or \
           (self.current_data_source == "google" and self.gads_raw_data is None):
            messagebox.showwarning("No Data", "Please load data first")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Validation Report",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialfile=f"{self.current_data_source}_ads_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            )

            if file_path:
                # Get quality report text
                quality_content = self.quality_text.get(1.0, tk.END)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(quality_content)

                messagebox.showinfo("Export Successful", f"Validation report exported to {file_path}")
                self.status_var.set(f"Exported validation report to {os.path.basename(file_path)}")

        except Exception as e:
            self.handle_error("Error exporting validation report", e)

    def export_data(self, data, default_name):
        """Generic data export function"""
        try:
            file_path = filedialog.asksaveasfilename(
                title=f"Save {default_name.replace('_', ' ').title()}",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialfile=f"{default_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            )

            if file_path:
                data.to_csv(file_path, index=False)
                messagebox.showinfo("Export Successful", f"Data exported to {file_path}")
                self.status_var.set(f"Exported {len(data)} records to {os.path.basename(file_path)}")

        except Exception as e:
            self.handle_error("Error exporting data", e)

    def handle_error(self, title, error):
        """Handle errors with detailed reporting"""
        error_msg = f"{title}: {str(error)}"
        print(f"ERROR: {error_msg}")
        print(traceback.format_exc())
        messagebox.showerror(title, error_msg)
        self.status_var.set(f"Error: {str(error)}")

def main():
    """Main application entry point with ttkbootstrap modern styling"""
    global BOOTSTRAP_AVAILABLE

    if BOOTSTRAP_AVAILABLE:
        # Create root with modern ttkbootstrap theme
        root = ttk.Window(themename="flatly")  # Modern flat theme
        print("Using ttkbootstrap modern theming")
        use_bootstrap = True
    else:
        # Fallback to standard tkinter with basic styling
        print("ttkbootstrap not found. Install with: pip install ttkbootstrap")
        print("Using basic tkinter styling...")
        root = tk.Tk()
        style = ttk.Style()
        style.theme_use('clam')
        use_bootstrap = False

    # Enhanced color scheme for professional appearance
    bg_color = '#f8f9fa'        # Light background
    accent_color = '#5294e2'     # Primary blue
    hover_color = '#27ae60'      # Success green
    pressed_color = '#1e7e34'    # Dark green
    text_color = '#212529'       # Dark text
    card_color = '#ffffff'       # White cards
    success_color = '#28a745'    # Success green
    warning_color = '#ffc107'    # Warning amber
    danger_color = '#dc3545'     # Danger red

    # Configure root window with enhanced styling
    root.configure(bg=bg_color)

    # Configure styling based on available theming
    if use_bootstrap:
        # ttkbootstrap provides built-in modern styling
        # No additional configuration needed - buttons will use bootstrap styles
        print("Using ttkbootstrap built-in styling")
    else:
        # Configure basic styling for fallback
        style.configure('Card.TFrame',
                       relief='flat',
                       borderwidth=0,
                       background=card_color)

        style.configure('Panel.TFrame',
                       relief='flat',
                       borderwidth=0,
                       background='#f8f9fa')

        # Basic button styling for fallback
        style.configure('TButton',
                       background=accent_color,
                       foreground='white',
                       borderwidth=0,
                       relief='flat',
                       font=('Segoe UI', 9),
                       padding=(12, 8))

        style.map('TButton',
                 background=[('active', hover_color),
                            ('pressed', pressed_color)])

        # Configure other basic styles
        style.configure('TLabelframe',
                       background=card_color,
                       borderwidth=0,
                       relief='flat')

        style.configure('TLabelframe.Label',
                       background=card_color,
                       foreground=text_color,
                       font=('Segoe UI', 11, 'bold'))

    # Additional styling only for fallback mode
    if not use_bootstrap:
        # Success and Warning button styles for fallback
        style.configure('Success.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       background=success_color,
                       foreground='white',
                       borderwidth=0,
                       relief='flat',
                       padding=(12, 8))

        style.map('Success.TButton',
                 background=[('active', '#218838'),
                            ('pressed', '#1e7e34')])

        style.configure('Warning.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       background=warning_color,
                       foreground='white',
                       borderwidth=0,
                       relief='flat',
                       padding=(12, 8))

        style.map('Warning.TButton',
                 background=[('active', '#e0a800'),
                            ('pressed', '#d39e00')])

    # Additional styling only for fallback mode
    if not use_bootstrap:
        # Enhanced Notebook styling for fallback
        style.configure('TNotebook',
                       background=bg_color,
                       borderwidth=0,
                       tabmargins=[2, 2, 2, 0])

        style.configure('TNotebook.Tab',
                       background='#ecf0f1',
                       foreground=text_color,
                       padding=[15, 10],
                       borderwidth=0,
                       relief='flat',
                       font=('Segoe UI', 9, 'bold'))

        style.map('TNotebook.Tab',
                 background=[('selected', card_color),
                            ('active', hover_color)],
                 foreground=[('selected', text_color),
                            ('active', 'white')])

        # Enhanced Frame styling for fallback
        style.configure('TFrame',
                       background=bg_color,
                       borderwidth=0)

        # Panel LabelFrame for left sidebar
        style.configure('Panel.TLabelframe',
                       background='#f8f9fa',
                       borderwidth=0,
                       relief='flat')

        style.configure('Panel.TLabelframe.Label',
                       background='#f8f9fa',
                       foreground=text_color,
                       font=('Segoe UI', 11, 'bold'))

    # Additional styling only for fallback mode
    if not use_bootstrap:
        # Enhanced Treeview styling for fallback
        style.configure('Treeview',
                       background=card_color,
                       foreground=text_color,
                       fieldbackground=card_color,
                       borderwidth=0,
                       relief='flat',
                       font=('Segoe UI', 9))

        style.configure('Treeview.Heading',
                       background='#ecf0f1',
                       foreground=text_color,
                       font=('Segoe UI', 9, 'bold'),
                       borderwidth=0,
                       relief='flat')

        style.map('Treeview',
                 background=[('selected', accent_color)],
                 foreground=[('selected', 'white')])

        # Enhanced Entry and Combobox styling for fallback
        style.configure('TEntry',
                       fieldbackground=card_color,
                       borderwidth=1,
                       relief='solid',
                       font=('Segoe UI', 9))

        style.configure('TCombobox',
                       fieldbackground=card_color,
                       borderwidth=1,
                       relief='solid',
                       font=('Segoe UI', 9))

        # Enhanced Label styling for fallback
        style.configure('TLabel',
                       background=bg_color,
                       foreground=text_color,
                       font=('Segoe UI', 9))

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 12, 'bold'),
                       background=bg_color,
                       foreground=text_color)

        # Enhanced Checkbutton and Radiobutton styling for fallback
        style.configure('TCheckbutton',
                       background=card_color,
                       foreground=text_color,
                       font=('Segoe UI', 9))

        style.configure('TRadiobutton',
                       background=card_color,
                       foreground=text_color,
                       font=('Segoe UI', 9))

    app = MetaAdsTransformerComplete(root)
    root.mainloop()

if __name__ == "__main__":
    main()
