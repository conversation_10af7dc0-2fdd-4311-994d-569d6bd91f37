#!/usr/bin/env python3
"""
Calculate exact totals from Google Ads CSV data to verify dashboard accuracy.
"""

import csv
import os

def calculate_gads_totals(csv_file_path):
    """Calculate total metrics from Google Ads CSV file."""
    
    # Initialize totals
    total_clicks = 0
    total_impressions = 0
    total_cost = 0.0
    total_records = 0
    
    # Lists to calculate averages
    ctr_values = []
    cpc_values = []
    
    print(f"📊 Analyzing Google Ads data from: {csv_file_path}")
    print("=" * 60)
    
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            
            for row in reader:
                total_records += 1
                
                # Parse numeric values safely
                try:
                    clicks = int(float(row['Clicks']) if row['Clicks'] else 0)
                    impressions = int(float(row['Impressions']) if row['Impressions'] else 0)
                    cost = float(row['Cost']) if row['Cost'] else 0.0
                    ctr = float(row['CTR']) if row['CTR'] else 0.0
                    cpc = float(row['CPC']) if row['CPC'] else 0.0
                    
                    # Add to totals
                    total_clicks += clicks
                    total_impressions += impressions
                    total_cost += cost
                    
                    # Collect CTR and CPC values for averaging (only non-zero values)
                    if ctr > 0:
                        ctr_values.append(ctr)
                    if cpc > 0:
                        cpc_values.append(cpc)
                        
                except (ValueError, TypeError) as e:
                    print(f"⚠️  Warning: Error parsing row {total_records}: {e}")
                    continue
    
    except FileNotFoundError:
        print(f"❌ Error: File not found: {csv_file_path}")
        return None
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return None
    
    # Calculate averages
    avg_ctr = (sum(ctr_values) / len(ctr_values)) * 100 if ctr_values else 0.0  # Convert to percentage
    avg_cpc = sum(cpc_values) / len(cpc_values) if cpc_values else 0.0
    
    # Calculate overall CTR from totals
    overall_ctr = (total_clicks / total_impressions) * 100 if total_impressions > 0 else 0.0
    
    # Print results
    print(f"📈 GOOGLE ADS TOTALS (ALL TIME)")
    print("=" * 60)
    print(f"Total Records:      {total_records:,}")
    print(f"Total Clicks:       {total_clicks:,}")
    print(f"Total Impressions:  {total_impressions:,}")
    print(f"Total Cost:         ${total_cost:,.2f}")
    print(f"Overall CTR:        {overall_ctr:.4f}%")
    print(f"Average CPC:        ${avg_cpc:.4f}")
    print()
    print("📊 CALCULATION DETAILS:")
    print(f"• CTR calculated from totals: {total_clicks:,} ÷ {total_impressions:,} × 100")
    print(f"• Average CPC from {len(cpc_values):,} non-zero CPC values")
    print(f"• Average CTR from {len(ctr_values):,} non-zero CTR values: {avg_ctr:.4f}%")
    
    return {
        'total_records': total_records,
        'total_clicks': total_clicks,
        'total_impressions': total_impressions,
        'total_cost': total_cost,
        'overall_ctr': overall_ctr,
        'avg_cpc': avg_cpc,
        'avg_ctr': avg_ctr
    }

if __name__ == "__main__":
    # Try different possible paths for the CSV file
    possible_paths = [
        "mtapp/gads.csv",
        "gads.csv",
        "../gads.csv"
    ]
    
    csv_path = None
    for path in possible_paths:
        if os.path.exists(path):
            csv_path = path
            break
    
    if csv_path:
        results = calculate_gads_totals(csv_path)
        if results:
            print("\n🎯 EXPECTED DASHBOARD VALUES:")
            print("=" * 60)
            print(f"Total Clicks:       {results['total_clicks']:,}")
            print(f"Total Impressions:  {results['total_impressions']:,}")
            print(f"Average CTR:        {results['overall_ctr']:.2f}%")
            print(f"Average CPC:        ${results['avg_cpc']:.2f}")
            print(f"Total Cost:         ${results['total_cost']:,.2f}")
    else:
        print("❌ Could not find gads.csv file in expected locations")
        print("Expected locations:")
        for path in possible_paths:
            print(f"  - {path}")
